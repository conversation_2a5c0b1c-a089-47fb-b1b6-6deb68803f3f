<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Бесплатен Калкулатор за Бременост - Калкулатор за Датум на Пораѓај и Гестациска Возраст 2025</title>
    <meta name="description" content="Пресметајте го датумот на пораѓај и гестациската возраст со нашиот бесплатен и прецизен калкулатор за бременост. Следете го вашето бременосно патување неделно врз основа на вашата последна менструација." />
    <meta name="keywords" content="калкулатор за бременост, калкулатор за датум на пораѓај, калкулатор за гестациска возраст, следење на бременост, недели на бременост, калкулатор за ЛМП, датум на пораѓај, идни мајки" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Бесплатен Калкулатор за Бременост - Калкулатор за Датум на Пораѓај и Гестациска Возраст" />
    <meta property="og:description" content="Пресметајте го датумот на пораѓај и гестациската возраст со нашиот бесплатен и прецизен калкулатор за бременост. Следете го вашето бременосно патување неделно." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/mk/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/mk/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/mk/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/mk/calculators/">
            <img
              width="200"
              src="/mk/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Лого на калкулатор"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Разговарајте со Август</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Бесплатен Калкулатор за Бременост и Калкулатор за Датум на Пораѓај</h1>
        <p>Пресметајте го датумот на пораѓај и следете ја гестациската возраст со нашиот прецизен и лесен за употреба калкулатор за бременост.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Има доверба од идните мајки и здравствените професионалци ширум светот</span>
          </div>

          <h2>Зошто да го користите нашиот калкулатор за бременост?</h2>
          <p>Нашиот бесплатен калкулатор за бременост ви помага да го одредите датумот на пораѓај на вашето бебе и тековната гестациска возраст врз основа на вашата последна менструација (ЛМП). Без разлика дали сте ново бремени или го следите вашето бременосно патување, оваа алатка обезбедува прецизни пресметки за да ви помогне да го планирате доаѓањето на вашето бебе.</p>

          <h3>Клучни карактеристики:</h3>
          <ul>
            <li>Инстантна пресметка на датумот на пораѓај врз основа на ЛМП</li>
            <li>Тековна гестациска возраст во недели и денови</li>
            <li>Лесен за употреба интерфејс со паѓачки менија</li>
            <li>Медицински прецизни пресметки според упатствата на СЗО</li>
            <li>Бесплатно за употреба без потреба од регистрација</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Пресметајте го вашиот датум на пораѓај</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Денешна дата:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Тековна дата" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Прв ден од последната менструација (ЛМП):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Дата на ЛМП" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Постави на денес
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Исчисти резултати
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Вашиот проценет датум на пораѓај:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Тековна гестациска возраст:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Често поставувани прашања за калкулаторите за бременост</h2>

        <div class="faq-item">
          <div class="faq-question">Колку е точен овој калкулатор за бременост?</div>
          <div class="faq-answer">Нашиот калкулатор за бременост ја користи стандардната медицинска формула за додавање на 280 дена на вашата последна менструација. Овој метод е приближно 95% точен за предвидување на датумот на пораѓај, иако индивидуалните бремености може да варираат до две недели.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Што ако не се сеќавам на точниот датум на мојата ЛМП?</div>
          <div class="faq-answer">Ако не се сеќавате на точниот датум на вашата ЛМП, обидете се да процените што е можно попрецизно. Вашиот лекар може да користи ултразвучни мерења за да обезбеди поточен датум на пораѓај за време на вашата прва пренатална посета.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Може ли да го користам овој калкулатор ако имам неправилни менструации?
          </div>
          <div class="faq-answer">Ако имате неправилни менструални циклуси, овој калкулатор може да биде помалку точен. Во такви случаи, вашиот лекар најверојатно ќе користи ултразвучно датирање за попрецизно утврдување на датумот на пораѓај.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Кога треба да ја закажам мојата прва пренатална посета?
          </div>
          <div class="faq-answer">
            Повеќето лекари препорачуваат закажување на првата пренатална посета помеѓу 6 и 8 недела од бременоста. Користете го нашиот калкулатор за да ја одредите вашата моментална гестациска возраст и соодветно планирајте.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Која е разликата помеѓу гестациската возраст и феталната возраст?
          </div>
          <div class="faq-answer">Гестациската возраст се пресметува од вашата последна менструација, додека феталната возраст се пресметува од зачнувањето (обично 2 недели подоцна). Медицинските професионалци обично ја користат гестациската возраст за поголема конзистентност.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
