<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>成人用BMI計算機 - 正確なBMIツール | MeetAugust</title>
    <meta
      name="description"
      content="高度なツールでBMIを計算しましょう。すぐに結果が表示され、体重カテゴリが分かります。無料、正確、科学的根拠あり。"
    />
    <meta
      name="keywords"
      content="bmi計算機, ボディマス指数, 体重カテゴリ, 健康計算機"
    />
    <meta name="author" content="MeetAugust" />
    <meta
      property="og:title"
      content="成人用BMI計算機 - 正確なBMIツール"
    />
    <meta
      property="og:description"
      content="BMIを計算して、体重カテゴリをすぐに確認しましょう。"
    />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="成人用BMI計算機 - 正確なBMIツール"
    />
    <meta
      name="twitter:description"
      content="BMIを計算して、体重カテゴリをすぐに確認しましょう。"
    />
    <link rel="canonical" href="https://www.meetaugust.ai/ja/calculators/bmi-calculator" />
    <link rel="icon" href="/ja/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/ja/calculators/bmi-calculator/style.css">
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
        <div class="navbar">
          <div class="logo">
            <a href="/ja/calculators/">
                <img
              width="200"
              src="/ja/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="計算機ロゴ"
            />
            </a>
          </div>
          <div class="nav">
            <div class="language-switcher" id="language-switcher">
              <select id="language-select">
                <!-- Options will be populated by JS -->
              </select>
            </div>
            <a
              href="https://app.meetaugust.ai/join/app?utm=bmi_calculator_topnav"
              class="talk-to-august"
              >Augustと話す</a
            >
          </div>
        </div>
      </header>
        <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">成人用BMI計算機</h1>
          <div class="page-info">
            <div class="info-badge">
              <div class="shield-icon">✓</div>
              <span>すべての方へ</span>
            </div>
          </div>
        </div>

        <div class="content-grid">
          <div class="info-section">
            <h2>成人のBMIについて知っておくべきこと</h2>
            <p class="info-text">
              成人用BMI計算機は、身長に対して体重が適切かどうかを評価する信頼できるツールです。 ...
            </p>

            <img
              src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BMI Scale"
              class="weight-image"
            />

            <div style="margin-top: 30px">
              <h3
                style="
                  color: var(--primary-color);
                  font-size: 20px;
                  margin-bottom: 15px;
                "
              >
                無料の成人用BMI計算機（20歳以上）
              </h3>
              <p style="color: var(--text-secondary); margin-bottom: 15px">
                この成人用BMI計算機を使って、すぐにBMIを算出し、あなたの体重カテゴリ（低体重、標準体重、過体重、肥満）を確認しましょう。この計算機は20歳以上の成人向けです。
              </p>
              <!-- <p style="color: var(--text-secondary)">
                BMIは健康状態や体重に関連するリスクを評価する多くのツールの一つです。他の臨床評価（病歴、生活習慣、身体検査、検査結果）と併せて解釈してください。 -->
                <!-- <a href="#" style="color: var(--primary-color)">
                  ボディマス指数について詳しく知る
                </a>
                そして、健康プロフィールにどのように関係するかを確認しましょう。個別のアドバイスについては必ず医療従事者にご相談ください。本ツールは教育目的のみであり、医療アドバイスの代わりにはなりません。
              </p> -->
            </div>
          </div>

          <div class="calculator-section">
            <div style="display: flex; justify-content: space-between;" >

              <h3 class="calculator-title">BMI計算機</h3>
              <button class="download-btn  neon-pulse" style="margin-left: 2rem; margin-top: 0; display: none; align-self: flex-start; font-size: 1.15rem; padding: 1rem 2rem;" id="download-pdf-btn" onclick="downloadBMIResultsPDF()"><span style="font-size:1.5em;vertical-align:middle;">📄</span> Download BMI Results</button>
            </div>

            <div class="unit-selector">
              <div class="unit-option">
                <input
                  type="radio"
                  id="us-units"
                  name="units"
                  value="us"
                  checked
                />
                <label for="us-units">米国単位</label>
              </div>
              <div class="unit-option">
                <input
                  type="radio"
                  id="metric-units"
                  name="units"
                  value="metric"
                />
                <label for="metric-units">メートル法単位</label>
              </div>
              <span class="reset-link" onclick="resetCalculator()">リセット</span>
            </div>

            <div class="form-group">
              <label class="form-label">身長</label>
              <div class="input-group" id="height-inputs">
                <input
                  type="number"
                  class="form-input"
                  id="height-feet"
                  placeholder="0"
                  min="0"
                  max="10"
                />
                <span class="unit-label">フィート (ft)</span>
                <input
                  type="number"
                  class="form-input"
                  id="height-inches"
                  placeholder="0"
                  min="0"
                  max="11"
                />
                <span class="unit-label">インチ (in)</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">体重</label>
              <div class="input-group">
                <input
                  type="number"
                  class="form-input"
                  id="weight"
                  placeholder="0"
                  min="0"
                />
                <span class="unit-label" id="weight-unit">ポンド (lbs)</span>
              </div>
            </div>

            <button class="calculate-btn" onclick="calculateBMI()">
              計算する
            </button>
          </div>
        </div>

        <div class="note-section">
          <p>
            <strong>注意：</strong> このBMI計算機はJavaScriptが必要です。ブラウザでJavaScriptが無効になっている場合や問題が発生した場合は、次の式で手動計算できます：
            <em>BMI = 体重（kg） / (身長（m） × 身長（m）)</em>
            例：体重70kg、身長1.75mの場合、BMIは22.9です。
          </p>
        </div>
      </main>

<!-- Result section moved below main content -->
<div class="result-section-wrapper">
  <div class="result-section" id="result-section">
    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
      <div>
        <div class="bmi-value" id="bmi-value">0.0</div>
        <div class="bmi-category" id="bmi-category"></div>
        <div class="bmi-description" id="bmi-description"></div>
      </div>
    </div>
  </div>
</div>

    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="/ja/calculators/bmi-calculator/script.js"></script>
    <script>
      const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
      const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
      const select = document.getElementById('language-select');
      const currentLang = window.location.pathname.split('/')[1] || 'en';
      locales.forEach(l => {
        const opt = document.createElement('option');
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener('change', function() {
        const newLang = this.value;
        let path = window.location.pathname.split('/');
        if (locales.includes(path[1])) { path[1] = newLang; }
        else { path = ['', newLang, ...path.slice(1)]; }
        localStorage.setItem('preferredLang', newLang);
        window.location.pathname = path.join('/');
      });
      // Persist language choice
      if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
        select.value = localStorage.getItem('preferredLang');
      }
    </script>
  </body>
</html> 