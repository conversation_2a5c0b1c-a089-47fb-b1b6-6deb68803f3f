// Month names array
const monthNames = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

// Initialize with today's date
function initializeTodayDate() {
  const today = new Date();
  // Set current date picker to today
  const currentDatePicker = document.getElementById("current_date_picker");
  if (currentDatePicker) {
    currentDatePicker.value = today.toISOString().split('T')[0];
  }
  // Set LMP date picker to today by default
  const lmpDatePicker = document.getElementById("lmp_date_picker");
  if (lmpDatePicker) {
    lmpDatePicker.value = today.toISOString().split('T')[0];
  }
}

// Set current date to today
function setToToday() {
  initializeTodayDate();
  calculatePregnancy();
}

// Clear results
function clearResults() {
  document.getElementById("due_date_result").textContent = "-";
  document.getElementById("gestational_age_result").textContent = "-";
}

// Validate date
function isValidDate(month, date, year) {
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

  // Check for leap year
  if (month === 1 && year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0)) {
    daysInMonth[1] = 29;
  }

  return date <= daysInMonth[month];
}

// Calculate pregnancy details
function calculatePregnancy() {
  // Get current date from the date picker
  const currentDateStr = document.getElementById("current_date_picker").value;
  if (!currentDateStr) {
    clearResults();
    return;
  }
  const currentDateObj = new Date(currentDateStr);
  const currentYear = currentDateObj.getFullYear();
  const currentMonth = currentDateObj.getMonth();
  const currentDate = currentDateObj.getDate();

  // Get LMP date from the date picker
  const lmpDateStr = document.getElementById("lmp_date_picker").value;
  if (!lmpDateStr) {
    clearResults();
    return;
  }
  const lmpDateObj = new Date(lmpDateStr);
  const lmpYear = lmpDateObj.getFullYear();
  const lmpMonth = lmpDateObj.getMonth();
  const lmpDate = lmpDateObj.getDate();

  // Validate dates
  if (!isValidDate(currentMonth, currentDate, currentYear)) {
    alert("Please enter a valid current date");
    clearResults();
    return;
  }

  if (!isValidDate(lmpMonth, lmpDate, lmpYear)) {
    alert("Please enter a valid LMP date");
    clearResults();
    return;
  }

  // Create date objects
  const currentDateTime = new Date(currentYear, currentMonth, currentDate);
  const lmpDateTime = new Date(lmpYear, lmpMonth, lmpDate);

  // Check if LMP is in the future
  if (lmpDateTime > currentDateTime) {
    alert("Your last menstrual period date cannot be in the future");
    clearResults();
    return;
  }

  // Calculate due date (LMP + 280 days)
  const dueDate = new Date(lmpDateTime.getTime() + 280 * 24 * 60 * 60 * 1000);

  // Calculate gestational age
  const timeDiff = currentDateTime.getTime() - lmpDateTime.getTime();
  const totalWeeks = timeDiff / (7 * 24 * 60 * 60 * 1000);
  const weeks = Math.floor(totalWeeks);
  const days = Math.round(7 * (totalWeeks - weeks));

  // Format results
  const dueDateString = `${monthNames[dueDate.getMonth()]} ${dueDate.getDate()}, ${dueDate.getFullYear()}`;
  const gestationalAgeString = `${weeks} weeks ${days} day${days !== 1 ? "s" : ""}`;

  // Display results
  document.getElementById("due_date_result").textContent = dueDateString;
  document.getElementById("gestational_age_result").textContent = gestationalAgeString;
}

// Add event listeners
document.addEventListener("DOMContentLoaded", function () {
  initializeTodayDate();
  calculatePregnancy();

  // Add change event listeners to the new date inputs
  const currentDatePicker = document.getElementById("current_date_picker");
  if (currentDatePicker) {
    currentDatePicker.addEventListener("change", calculatePregnancy);
  }
  const lmpDatePicker = document.getElementById("lmp_date_picker");
  if (lmpDatePicker) {
    lmpDatePicker.addEventListener("change", calculatePregnancy);
  }
});

// Header scroll effect
function handleScroll() {
    const header = document.querySelector("header");
    if (header) {
      if (window.scrollY === 0) {
        header.classList.remove("scrolled");
      } else {
        header.classList.add("scrolled");
      }
    }
  }
  
  // Add scroll event listener
  window.addEventListener("scroll", handleScroll);
  
  // Initialize header state
  handleScroll();
