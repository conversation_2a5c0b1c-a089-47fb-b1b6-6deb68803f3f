'use client'
import React, { useState, useRef } from 'react';

const DUMMY_RESPONSE = 'This is a demo answer. Please consult a professional for medical advice.';
const MORE_QUESTIONS_URL = 'https://app.meetaugust.ai/join/wa?message=Hello%20August&utm=ask_questions_widget';

type Message = {
  sender: 'user' | 'bot';
  text: string;
  isLoading?: boolean;
};

// Animated loading dots component (medium size)
const LoadingDots = () => (
  <span className="inline-flex items-center gap-1 h-5">
    <span className="loading-dot bg-gray-400" />
    <span className="loading-dot bg-gray-400" />
    <span className="loading-dot bg-gray-400" />
    <style jsx>{`
      .loading-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin: 0 1px;
        animation: bounce 1.2s infinite both;
      }
      .loading-dot:nth-child(1) { animation-delay: 0s; }
      .loading-dot:nth-child(2) { animation-delay: 0.2s; }
      .loading-dot:nth-child(3) { animation-delay: 0.4s; }
      @keyframes bounce {
        0%, 80%, 100% { transform: scale(1); opacity: 0.7; }
        40% { transform: scale(1.4); opacity: 1; }
      }
    `}</style>
  </span>
);

const   AskQuestions = () => {
  const [question, setQuestion] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [stage, setStage] = useState<'input'|'chat'|'done'>('input');
  const [loading, setLoading] = useState(false);
  const chatEndRef = useRef<HTMLDivElement | null>(null);

  const handleSubmit = (e: any) => {
    e.preventDefault();
    if (!question.trim()) return;
    setStage('chat');
    setLoading(true);
    // Add user message and loading bot bubble
    setMessages([
      { sender: 'user', text: question },
      { sender: 'bot', text: '', isLoading: true }
    ]);
    setQuestion('');
    setTimeout(() => {
      setMessages([
        { sender: 'user', text: question },
        { sender: 'bot', text: DUMMY_RESPONSE }
      ]);
      setLoading(false);
      setStage('done');
    }, 1200);
  };

  return (
    <div id="ask-questions-widget" className="max-w-2xl mx-auto p-6 bg-white">
      {/* Header Section */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center">
            <div className="text-white text-lg font-bold">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="size-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M8.625 12a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 0 1-2.555-.337A5.972 5.972 0 0 1 5.41 20.97a5.969 5.969 0 0 1-.474-.065 4.48 4.48 0 0 0 .978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25Z"
                />
              </svg>
            </div>
          </div>
          <h1 className="text-3xl font-semibold text-gray-800">
            Have Questions?
          </h1>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Have a question on this topic? Submit it here and get an instant
          answer from our{" "}
          <a href={MORE_QUESTIONS_URL} className="text-teal-600 font-medium">
            AI Doctor
          </a>
          .
        </p>
      </div>
      <div className="chat-embed mx-auto border border-grey-600 pl-[32px] rounded-lg  " style={{ height: "100%", width: "100%" }} />


      {/* Disclaimer */}
       
    </div>
  );
};

export default AskQuestions;