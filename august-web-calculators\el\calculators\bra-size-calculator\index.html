<!DOCTYPE html>
<html lang="el">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>Υπολογιστής Μεγέθους Σουτιέν | Βρείτε την Τέλεια Εφαρμογή</title>
    <meta name="description" content="Υπολογίστε εύκολα το ιδανικό σας μέγεθος σουτιέν με τον ακριβή μας υπολογιστή που βασίζεται στην επιστήμη. Αποκτήστε την τέλεια εφαρμογή για άνεση, υποστήριξη και αυτοπεποίθηση. Υποστηρίζει ινδικά και διεθνή μεγέθη." />
    <meta name="keywords" content="υπολογιστής μεγέθους σουτιέν, εφαρ<PERSON><PERSON><PERSON><PERSON> σουτιέν, κάτω από το στήθος, πάνω από το στήθος, μέγεθος κούπας, ινδικές γυναίκες, εφαρμογή εσωρούχων, μέτρηση σουτιέν, πώς να μετρήσετε το μέγεθος σουτιέν, καλύτερος υπολογιστής μεγέθους σουτιέν" />
    <meta name="author" content="MeetAugust" />
    <meta property="og:title" content="Υπολογιστής Μεγέθους Σουτιέν | Βρείτε την Τέλεια Εφαρμογή" />
    <meta property="og:description" content="Υπολογίστε εύκολα το ιδανικό σας μέγεθος σουτιέν με τον ακριβή μας υπολογιστή που βασίζεται στην επιστήμη. Αποκτήστε την τέλεια εφαρμογή για άνεση, υποστήριξη και αυτοπεποίθηση." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Υπολογιστής Μεγέθους Σουτιέν | Βρείτε την Τέλεια Εφαρμογή" />
    <meta name="twitter:description" content="Υπολογίστε εύκολα το ιδανικό σας μέγεθος σουτιέν με τον ακριβή μας υπολογιστή που βασίζεται στην επιστήμη. Αποκτήστε την τέλεια εφαρμογή για άνεση, υποστήριξη και αυτοπεποίθηση." />
    <link rel="canonical" href="https://www.meetaugust.ai/bra-size-calculator" />
    <link rel="stylesheet" href="style.css">
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <img width="200" src="https://res.cloudinary.com/dpgnd3ad7/image/upload/v1738557729/august_logo_green_nd4fn9.svg" alt="Λογότυπο August" />
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select" style="border:1px solid #e5e7eb;border-radius:6px;padding:6px 8px;font-size:14px;color:#374151;background:#fff;outline:none;"></select>
          </div>
          <a href="https://app.meetaugust.ai/redirect/wa?message=Hello%20August" class="talk-to-august">Μιλήστε με τον August</a>
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">Υπολογιστής Μεγέθους Σουτιέν</h1>
        </div>
        <div class="content-grid">
          <div class="info-section">
            <h2>Πώς να Μετρήσετε το Μέγεθος του Σουτιέν σας</h2>
            <div class="measurement-steps">
              <div class="step-card">
                <div class="step-icon">1</div>
                <div class="step-content">
                  <strong>Μέγεθος Ζώνης</strong>
                  <p style="padding: 0;margin: 0;">
                    Μετρήστε κάτω από το στήθος σας. <br>
                    Στρογγυλοποιήστε στον πλησιέστερο ακέραιο αριθμό. <br>
                    <span class="badge">Αν είναι ζυγός, προσθέστε 4</span>
                    <span class="badge">Αν είναι μονός, προσθέστε 5</span><br>
                    <span style="color:var(--primary-color);font-weight:500;">Αυτό είναι το μέγεθος της ζώνης σας.</span>
                  </p>
                </div>
              </div>
              <div class="step-card">
                <div class="step-icon">2</div>
                <div class="step-content">
                  <strong>Μέγεθος Κούπας</strong>
                  <p style="padding: 0;margin: 0;">
                    Μετρήστε γύρω από το πιο γεμάτο μέρος του στήθους σας. <br>
                    Αφαιρέστε το μέγεθος της ζώνης από αυτόν τον αριθμό. <br>
                    <span style="color:var(--primary-color);font-weight:500;">Κάθε ίντσα διαφοράς = 1 μέγεθος κούπας (A, B, C κ.λπ.)</span>
                  </p>
                </div>
              </div>
              <div class="step-card tip-card">
                <div class="step-icon">💡</div>
                <div class="step-content">
                  <strong>Συμβουλή</strong>
                  <p style="padding: 0;margin: 0;">
                    Φορέστε σουτιέν χωρίς επένδυση για καλύτερα αποτελέσματα. <br>
                    Πάντα στρογγυλοποιείτε προς τα πάνω αν προκύψει κλάσμα.
                  </p>
                </div>
              </div>
            </div>
            <h3 style="margin-top:2em;">Πίνακας Μεγεθών Σουτιέν σε Ίντσες</h3>
            <table class="bmi-ranges-table" aria-label="Πίνακας Μεγεθών Σουτιέν σε Ίντσες">
              <thead>
                <tr>
                  <th>Μέγεθος Ζώνης</th>
                  <th>Κάτω από το στήθος (ίντσες)</th>
                  <th>Πάνω από το στήθος για A</th>
                  <th>Πάνω από το στήθος για B</th>
                  <th>Πάνω από το στήθος για C</th>
                  <th>Πάνω από το στήθος για D</th>
                  <th>Πάνω από το στήθος για DD/E</th>
                  <th>Πάνω από το στήθος για F</th>
                  <th>Πάνω από το στήθος για G</th>
                </tr>
              </thead>
              <tbody>
                <tr><td>28</td><td>23–24</td><td>28–29</td><td>29–30</td><td>30–31</td><td>31–32</td><td>32–33</td><td>33–34</td><td>34–35</td></tr>
                <tr><td>30</td><td>25–26</td><td>30–31</td><td>31–32</td><td>32–33</td><td>33–34</td><td>34–35</td><td>35–36</td><td>36–37</td></tr>
                <tr><td>32</td><td>27–28</td><td>32–33</td><td>33–34</td><td>34–35</td><td>35–36</td><td>36–37</td><td>37–38</td><td>38–39</td></tr>
                <tr><td>34</td><td>29–30</td><td>34–35</td><td>35–36</td><td>36–37</td><td>37–38</td><td>38–39</td><td>39–40</td><td>40–41</td></tr>
                <tr><td>36</td><td>31–32</td><td>36–37</td><td>37–38</td><td>38–39</td><td>39–40</td><td>40–41</td><td>41–42</td><td>42–43</td></tr>
                <tr><td>38</td><td>33–34</td><td>38–39</td><td>39–40</td><td>40–41</td><td>41–42</td><td>42–43</td><td>43–44</td><td>44–45</td></tr>
                <tr><td>40</td><td>35–36</td><td>40–41</td><td>41–42</td><td>42–43</td><td>43–44</td><td>44–45</td><td>45–46</td><td>46–47</td></tr>
                <tr><td>42</td><td>37–38</td><td>42–43</td><td>43–44</td><td>44–45</td><td>45–46</td><td>46–47</td><td>47–48</td><td>48–49</td></tr>
                <tr><td>44</td><td>39–40</td><td>44–45</td><td>45–46</td><td>46–47</td><td>47–48</td><td>48–49</td><td>49–50</td><td>50–51</td></tr>
              </tbody>
            </table>
            <h3 style="margin-top:2em;">Πίνακας Μεγεθών Σουτιέν σε Εκατοστά</h3>
            <table class="bmi-ranges-table" aria-label="Πίνακας Μεγεθών Σουτιέν σε Εκατοστά">
              <thead>
                <tr>
                  <th>Μέγεθος Ζώνης</th>
                  <th>Κάτω από το στήθος (εκ)</th>
                  <th>Πάνω από το στήθος για A</th>
                  <th>Πάνω από το στήθος για B</th>
                  <th>Πάνω από το στήθος για C</th>
                  <th>Πάνω από το στήθος για D</th>
                  <th>Πάνω από το στήθος για DD/E</th>
                  <th>Πάνω από το στήθος για F</th>
                  <th>Πάνω από το στήθος για G</th>
                </tr>
              </thead>
              <tbody>
                <tr><td>28</td><td>58.5–61</td><td>71.5–74</td><td>74–76.5</td><td>76.5–79</td><td>79–81.5</td><td>81.5–84</td><td>84–86.5</td><td>86.5–89</td></tr>
                <tr><td>30</td><td>63.5–66</td><td>76.5–79</td><td>79–81.5</td><td>81.5–84</td><td>84–86.5</td><td>86.5–89</td><td>89–91.5</td><td>91.5–94</td></tr>
                <tr><td>32</td><td>68.5–71</td><td>81.5–84</td><td>84–86.5</td><td>86.5–89</td><td>89–91.5</td><td>91.5–94</td><td>94–96.5</td><td>96.5–99</td></tr>
                <tr><td>34</td><td>73.5–76</td><td>86.5–89</td><td>89–91.5</td><td>91.5–94</td><td>94–96.5</td><td>96.5–99</td><td>99–101.5</td><td>101.5–104</td></tr>
                <tr><td>36</td><td>78.5–81</td><td>91.5–94</td><td>94–96.5</td><td>96.5–99</td><td>99–101.5</td><td>101.5–104</td><td>104–106.5</td><td>106.5–109</td></tr>
                <tr><td>38</td><td>83.5–86</td><td>96.5–99</td><td>99–101.5</td><td>101.5–104</td><td>104–106.5</td><td>106.5–109</td><td>109–111.5</td><td>111.5–114</td></tr>
                <tr><td>40</td><td>88.5–91</td><td>101.5–104</td><td>104–106.5</td><td>106.5–109</td><td>109–111.5</td><td>111.5–114</td><td>114–116.5</td><td>116.5–119</td></tr>
                <tr><td>42</td><td>93.5–96</td><td>106.5–109</td><td>109–111.5</td><td>111.5–114</td><td>114–116.5</td><td>116.5–119</td><td>119–121.5</td><td>121.5–124</td></tr>
                <tr><td>44</td><td>98.5–101</td><td>111.5–114</td><td>114–116.5</td><td>116.5–119</td><td>119–121.5</td><td>121.5–124</td><td>124–126.5</td><td>126.5–129</td></tr>
              </tbody>
            </table>
            <div class="note-section">
              <p><strong>Επαγγελματική Συμβουλή:</strong> Ελέγξτε το μέγεθος του σουτιέν σας κάθε 6 μήνες. Η σωστή εφαρμογή βελτιώνει την άνεση, τη στάση και την αυτοπεποίθηση!</p>
            </div>
          </div>
          <div class="calculator-section">
            <h2 class="calculator-title">Βρείτε το Μέγεθος του Σουτιέν σας</h2>
            <div class="unit-selector">
              <div class="unit-option">
                <input type="radio" id="in-units" name="units" value="in" checked />
                <label for="in-units">ίντσες</label>
              </div>
              <div class="unit-option">
                <input type="radio" id="cm-units" name="units" value="cm" />
                <label for="cm-units">εκ</label>
              </div>
              <span class="reset-link" onclick="resetBraCalculator()">ΕΠΑΝΑΦΟΡΑ</span>
            </div>
            <div class="form-group">
              <label class="form-label">Κάτω από το στήθος</label>
              <div class="input-group" id="underbust-inputs">
                <input type="number" class="form-input" id="underbust" placeholder="0" min="0" />
                <span class="unit-label" id="underbust-unit">ίντσες</span>
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">Πάνω από το στήθος</label>
              <div class="input-group" id="overbust-inputs">
                <input type="number" class="form-input" id="overbust" placeholder="0" min="0" />
                <span class="unit-label" id="overbust-unit">ίντσες</span>
              </div>
            </div>
            <button class="calculate-btn" onclick="calculateBraSize()">Υπολογίστε το Μέγεθος</button>
            <div class="result-section" id="bra-result-section" style="display:none; margin-top:30px;">
              <div class="calculated-results" id="bra-size-result"></div>
            </div>
          </div>
        </div>
      </main>
    </div>
    <script src="script.js"></script>
    <script>
    const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
    const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
    const select = document.getElementById('language-select');
    const currentLang = window.location.pathname.split('/')[1] || 'en';
    locales.forEach(l => {
      const opt = document.createElement('option');
      opt.value = l;
      opt.textContent = languageNames[l] || l;
      if (l === currentLang) opt.selected = true;
      select.appendChild(opt);
    });
    select.addEventListener('change', function() {
      const newLang = this.value;
      let path = window.location.pathname.split('/');
      if (locales.includes(path[1])) { path[1] = newLang; }
      else { path = ['', newLang, ...path.slice(1)]; }
      localStorage.setItem('preferredLang', newLang);
      window.location.pathname = path.join('/');
    });
    // Persist language choice
    if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
      select.value = localStorage.getItem('preferredLang');
      select.dispatchEvent(new Event('change'));
    }
    </script>
  </body>
</html>