<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calcolatore di Gravidanza Gratuito - Calcolatore della Data di Parto e dell'Età Gestazionale 2025</title>
    <meta name="description" content="Calcola la tua data di parto e l'età gestazionale con il nostro calcolatore di gravidanza gratuito e accurato. Segui il tuo percorso di gravidanza settimana per settimana in base al tuo ultimo periodo mestruale." />
    <meta name="keywords" content="calcolatore di gravidanza, calcolatore della data di parto, calcolatore dell'età gestazionale, tracciamento della gravidanza, settimane di gravidanza, calcolatore LMP, data di parto, future mamme" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Calcolatore di Gravidanza Gratuito - Calcolatore della Data di Parto e dell'Età Gestazionale" />
    <meta property="og:description" content="Calcola la tua data di parto e l'età gestazionale con il nostro calcolatore di gravidanza gratuito e accurato. Segui il tuo percorso di gravidanza settimana per settimana." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/it/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/it/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/it/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/it/calculators/">
            <img
              width="200"
              src="/it/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo Calcolatore"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Parla con August</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Calcolatore di Gravidanza Gratuito e Calcolatore della Data di Parto</h1>
        <p>Calcola la tua data di parto e segui la tua età gestazionale con il nostro calcolatore di gravidanza accurato e facile da usare.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Scelto da future mamme e professionisti sanitari in tutto il mondo</span>
          </div>

          <h2>Perché usare il nostro calcolatore di gravidanza?</h2>
          <p>Il nostro calcolatore di gravidanza gratuito ti aiuta a determinare la data di parto del tuo bambino e la tua attuale età gestazionale in base al tuo ultimo periodo mestruale (LMP). Che tu sia appena incinta o stia seguendo il tuo percorso di gravidanza, questo strumento fornisce calcoli accurati per aiutarti a pianificare l'arrivo del tuo bambino.</p>

          <h3>Caratteristiche principali:</h3>
          <ul>
            <li>Calcolo istantaneo della data di parto basato sull'LMP</li>
            <li>Età gestazionale attuale in settimane e giorni</li>
            <li>Interfaccia facile da usare con menu a discesa</li>
            <li>Calcoli medicalmente accurati seguendo le linee guida dell'OMS</li>
            <li>Gratuito senza registrazione richiesta</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Calcola la tua Data di Parto</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Data odierna:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Data attuale" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Primo giorno dell'ultimo periodo mestruale (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Data LMP" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Imposta su oggi
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Cancella risultati
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">La tua data di parto stimata:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Età gestazionale attuale:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Domande frequenti sui calcolatori di gravidanza</h2>

        <div class="faq-item">
          <div class="faq-question">Quanto è accurato questo calcolatore di gravidanza?</div>
          <div class="faq-answer">Il nostro calcolatore di gravidanza utilizza la formula medica standard di aggiungere 280 giorni al tuo ultimo periodo mestruale. Questo metodo è accurato al 95% circa per prevedere la tua data di parto, anche se le gravidanze individuali possono variare fino a due settimane.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Cosa succede se non ricordo la data esatta del mio LMP?</div>
          <div class="faq-answer">Se non ricordi la data esatta del tuo LMP, cerca di stimare il più vicino possibile. Il tuo medico può utilizzare misurazioni ecografiche per fornire una data di parto più precisa durante la tua prima visita prenatale.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Posso usare questo calcolatore se ho cicli mestruali irregolari?
          </div>
          <div class="faq-answer">Se hai cicli mestruali irregolari, questo calcolatore potrebbe essere meno accurato. In tali casi, il tuo medico probabilmente utilizzerà la datazione ecografica per determinare la tua data di parto con maggiore precisione.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Quando dovrei programmare il mio primo appuntamento prenatale?
          </div>
          <div class="faq-answer">
            La maggior parte dei medici consiglia di programmare il primo appuntamento prenatale tra le 6 e le 8 settimane di gravidanza. Usa il nostro calcolatore per determinare la tua attuale età gestazionale e pianifica di conseguenza.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Qual è la differenza tra età gestazionale ed età fetale?
          </div>
          <div class="faq-answer">L'età gestazionale viene calcolata a partire dal tuo ultimo periodo mestruale, mentre l'età fetale viene calcolata dalla concezione (di solito 2 settimane dopo). I professionisti medici usano generalmente l'età gestazionale per maggiore coerenza.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
