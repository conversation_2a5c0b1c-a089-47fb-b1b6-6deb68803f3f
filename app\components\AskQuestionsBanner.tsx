import React from 'react'

const AskQuestionsBanner = () => {
  const handleScrollToWidget = () => {
    const el = document.getElementById('ask-questions-widget');
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };
  return (
    <div className="max-w-4xl mx-auto ">
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          background: "#f6f6f7",
          borderRadius: "10px",
          padding: "14px 32px",
          margin: "20px 0",
          boxSizing: "border-box",
        }}
      >
        <span
          style={{
            color: "#232a36",
            fontSize: "1.4rem",
            fontWeight: 500,
          }}
        >
          Question on this topic? Get an instant answer from <b>AI Doctor</b>.
        </span>
        <button
          onClick={handleScrollToWidget}
          style={{
            background: "#179189",
            color: "#fff",
            border: "none",
            borderRadius: "6px",
            padding: "10px 32px",
            fontSize: "1.3rem",
            fontWeight: 600,
            cursor: "pointer",
            transition: "background 0.2s",
          }}
        >
          Ask Question
        </button>
      </div>
    </div>
  );
}

export default AskQuestionsBanner