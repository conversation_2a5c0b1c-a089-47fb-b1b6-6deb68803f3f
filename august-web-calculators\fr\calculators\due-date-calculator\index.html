<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calculateur de Grossesse Gratuit - Calculateur de Date d'Accouchement et d'Âge Gestationnel 2025</title>
    <meta name="description" content="Calculez votre date d'accouchement et votre âge gestationnel avec notre calculateur de grossesse gratuit et précis. Suivez votre parcours de grossesse semaine par semaine en fonction de votre dernière période menstruelle." />
    <meta name="keywords" content="calculateur de grossesse, calculateur de date d'accouchement, calculateur d'âge gestationnel, suivi de grossesse, semaines de grossesse, calculateur de DMP, date d'accouchement, futures mamans" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Calculateur de Grossesse Gratuit - Calculateur de Date d'Accouchement et d'Âge Gestationnel" />
    <meta property="og:description" content="Calculez votre date d'accouchement et votre âge gestationnel avec notre calculateur de grossesse gratuit et précis. Suivez votre parcours de grossesse semaine par semaine." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/fr/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/fr/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/fr/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/fr/calculators/">
            <img
              width="200"
              src="/fr/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo du Calculateur"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Parler à August</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Calculateur de date d'accouchement</h1>
        <p>Estimez la date d'accouchement de votre bébé en fonction de votre dernière période menstruelle ou de la date de conception. Utilisez notre calculateur de date d'accouchement pour planifier votre grossesse et vous préparer à l'arrivée de votre bébé.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Approuvé par les futures mamans et les professionnels de santé dans le monde entier</span>
          </div>

          <h2>Pourquoi utiliser notre calculateur de grossesse ?</h2>
          <p>Notre calculateur de grossesse gratuit vous aide à déterminer la date d'accouchement de votre bébé et votre âge gestationnel actuel en fonction de votre dernière période menstruelle (DMP). Que vous soyez nouvellement enceinte ou que vous suiviez votre parcours de grossesse, cet outil fournit des calculs précis pour vous aider à planifier l'arrivée de votre bébé.</p>

          <h3>Caractéristiques principales :</h3>
          <ul>
            <li>Calcul instantané de la date d'accouchement basé sur la DMP</li>
            <li>Âge gestationnel actuel en semaines et jours</li>
            <li>Interface facile à utiliser avec des menus déroulants</li>
            <li>Calculs médicalement précis suivant les directives de l'OMS</li>
            <li>Gratuit à utiliser sans inscription requise</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Calculez votre date d'accouchement</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Date d'aujourd'hui :</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Date actuelle" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Premier jour de la dernière période menstruelle (DMP) :</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Date de la DMP" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Définir à aujourd'hui
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Effacer les résultats
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Votre date d'accouchement estimée :</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Âge gestationnel actuel :
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Questions fréquentes sur les calculateurs de grossesse</h2>

        <div class="faq-item">
          <div class="faq-question">Quelle est la précision de ce calculateur de grossesse ?</div>
          <div class="faq-answer">Notre calculateur de grossesse utilise la formule médicale standard consistant à ajouter 280 jours à votre dernière période menstruelle. Cette méthode est précise à environ 95 % pour prédire votre date d'accouchement, bien que les grossesses individuelles puissent varier jusqu'à deux semaines.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Et si je ne me souviens pas de la date exacte de ma DMP ?</div>
          <div class="faq-answer">Si vous ne vous souvenez pas de la date exacte de votre DMP, essayez d'estimer au plus près. Votre professionnel de santé peut utiliser des mesures échographiques pour fournir une date d'accouchement plus précise lors de votre première visite prénatale.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Puis-je utiliser ce calculateur si j'ai des cycles menstruels irréguliers ?
          </div>
          <div class="faq-answer">Si vous avez des cycles menstruels irréguliers, ce calculateur peut être moins précis. Dans ce cas, votre médecin utilisera probablement la datation par échographie pour déterminer votre date d'accouchement plus précisément.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Quand dois-je planifier mon premier rendez-vous prénatal ?
          </div>
          <div class="faq-answer">
            La plupart des professionnels de santé recommandent de planifier votre premier rendez-vous prénatal entre 6 et 8 semaines de grossesse. Utilisez notre calculateur pour déterminer votre âge gestationnel actuel et planifiez en conséquence.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Quelle est la différence entre l'âge gestationnel et l'âge fœtal ?
          </div>
          <div class="faq-answer">L'âge gestationnel est calculé à partir de votre dernière période menstruelle, tandis que l'âge fœtal est calculé à partir de la conception (généralement 2 semaines plus tard). Les professionnels médicaux utilisent généralement l'âge gestationnel pour plus de cohérence.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
