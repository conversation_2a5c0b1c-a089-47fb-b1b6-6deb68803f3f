<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calculadora de Embarazo Gratuita - Calculadora de Fecha de Parto y Edad Gestacional 2025</title>
    <meta name="description" content="Calcula tu fecha de parto y edad gestacional con nuestra calculadora de embarazo gratuita y precisa. Sigue tu viaje de embarazo semana a semana basado en tu última menstruación." />
    <meta name="keywords" content="calculadora de embarazo, calculadora de fecha de parto, calculadora de edad gestacional, seguimiento de embarazo, semanas de embarazo, calculadora de LMP, fecha de parto, futuras mamás" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Calculadora de Embarazo Gratuita - Calculadora de Fecha de Parto y Edad Gestacional" />
    <meta property="og:description" content="Calcula tu fecha de parto y edad gestacional con nuestra calculadora de embarazo gratuita y precisa. Sigue tu viaje de embarazo semana a semana." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/es/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/es/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/es/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/es/calculators/">
            <img
              width="200"
              src="/es/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo de Calculadora"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Habla con August</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Calculadora de Embarazo Gratuita y Calculadora de Fecha de Parto</h1>
        <p>Calcula tu fecha de parto y sigue tu edad gestacional con nuestra calculadora de embarazo precisa y fácil de usar.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Confiado por futuras mamás y profesionales de la salud en todo el mundo</span>
          </div>

          <h2>¿Por qué usar nuestra calculadora de embarazo?</h2>
          <p>Nuestra calculadora de embarazo gratuita te ayuda a determinar la fecha de parto de tu bebé y tu edad gestacional actual basada en tu última menstruación (LMP). Ya sea que estés recién embarazada o siguiendo tu viaje de embarazo, esta herramienta proporciona cálculos precisos para ayudarte a計劃ar la llegada de tu bebé.</p>

          <h3>Características principales:</h3>
          <ul>
            <li>Cálculo instantáneo de la fecha de parto basado en la LMP</li>
            <li>Edad gestacional actual en semanas y días</li>
            <li>Interfaz fácil de usar con menús desplegables</li>
            <li>Cálculos médicamente precisos siguiendo las directrices de la OMS</li>
            <li>Gratis para usar sin registro requerido</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Calcula tu Fecha de Parto</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Fecha de hoy:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Fecha actual" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Primer día de la última menstruación (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Fecha de la LMP" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Establecer en hoy
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Borrar resultados
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Tu fecha de parto estimada:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Edad gestacional actual:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Preguntas frecuentes sobre calculadoras de embarazo</h2>

        <div class="faq-item">
          <div class="faq-question">¿Qué tan precisa es esta calculadora de embarazo?</div>
          <div class="faq-answer">Nuestra calculadora de embarazo utiliza la fórmula médica estándar de sumar 280 días a tu última menstruación. Este método es aproximadamente 95% preciso para predecir tu fecha de parto, aunque los embarazos individuales pueden variar hasta dos semanas.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">¿Qué pasa si no recuerdo la fecha exacta de mi LMP?</div>
          <div class="faq-answer">Si no recuerdas la fecha exacta de tu LMP, intenta estimar lo más cerca posible. Tu proveedor de atención médica puede usar medidas de ultrasonido para proporcionar una fecha de parto más precisa durante tu primera visita prenatal.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            ¿Puedo usar esta calculadora si tengo períodos irregulares?
          </div>
          <div class="faq-answer">Si tienes ciclos menstruales irregulares, esta calculadora puede ser menos precisa. En tales casos, tu médico probablemente usará datación por ultrasonido para determinar tu fecha de parto con mayor precisión.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            ¿Cuándo debo programar mi primera cita prenatal?
          </div>
          <div class="faq-answer">
            La mayoría de los proveedores de atención médica recomiendan programar tu primera cita prenatal entre las 6 y 8 semanas de embarazo. Usa nuestra calculadora para determinar tu edad gestacional actual y planificar en consecuencia.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            ¿Cuál es la diferencia entre la edad gestacional y la edad fetal?
          </div>
          <div class="faq-answer">La edad gestacional se calcula desde tu última menstruación, mientras que la edad fetal se calcula desde la concepción (generalmente 2 semanas después). Los profesionales médicos suelen usar la edad gestacional para mayor consistencia.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
