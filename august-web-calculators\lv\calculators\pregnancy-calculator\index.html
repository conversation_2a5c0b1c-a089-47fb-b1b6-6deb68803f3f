<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bezmaksas grūtniecības kalkulators - Dzemdību datuma un gestācijas vecuma kalkulators 2025</title>
    <meta name="description" content="Aprēķiniet savu grūtniecības dzemdību datumu un gestācijas vecumu ar mūsu bezmaksas, precīzo grūtniecības kalkulatoru. Sekojiet savai grūtniecības gaitai nedēļu pēc nedēļas, pamatojoties uz pēdējo menstruāciju." />
    <meta name="keywords" content="grūtniecības kalkulators, dzemdību datuma kalkulators, gestācijas vecuma kalkulators, grūtniec<PERSON><PERSON> izsekotājs, gr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nedēļas, LMP kalkulators, gr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dzemd<PERSON>bu datums, topošās mātes" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Bezmaksas grūtniecības kalkulators - Dzemdību datuma un gestācijas vecuma kalkulators" />
    <meta property="og:description" content="Aprēķiniet savu grūtniecības dzemdību datumu un gestācijas vecumu ar mūsu bezmaksas, precīzo grūtniecības kalkulatoru. Sekojiet savai grūtniecības gaitai nedēļu pēc nedēļas." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/lv/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/lv/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/lv/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/lv/calculators/">
            <img
              width="200"
              src="/lv/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Kalkulatora Logo"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Sarunāties ar Augustu</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Bezmaksas grūtniecības kalkulators un dzemdību datuma kalkulators</h1>
        <p>Aprēķiniet savu grūtniecības dzemdību datumu un sekojiet gestācijas vecumam ar mūsu precīzo, viegli lietojamo grūtniecības kalkulatoru</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Uzticas topošās mātes un veselības aprūpes speciālisti visā pasaulē</span>
          </div>

          <h2>Kāpēc izmantot mūsu grūtniecības kalkulatoru?</h2>
          <p>Mūsu bezmaksas grūtniecības kalkulators palīdz noteikt jūsu mazuļa dzemdību datumu un pašreizējo gestācijas vecumu, pamatojoties uz pēdējo menstruāciju (LMP). Neatkarīgi no tā, vai esat tikko kļuvusi grūtna vai sekojat grūtniecības gaitai, šis rīks nodrošina precīzus aprēķinus, lai palīdzētu jums plānot mazuļa ierašanos.</p>

          <h3>Galvenās funkcijas:</h3>
          <ul>
            <li>Tūlītējs dzemdību datuma aprēķins, pamatojoties uz LMP</li>
            <li>Pašreizējais gestācijas vecums nedēļās un dienās</li>
            <li>Viegli lietojams interfeiss ar nolaižamo izvēlni</li>
            <li>Medicīniski precīzi aprēķini, ievērojot PVO vadlīnijas</li>
            <li>Bezmaksas lietošana bez reģistrācijas</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Aprēķiniet savu dzemdību datumu</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Šodienas datums:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Pašreizējais datums" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Pēdējās menstruācijas pirmā diena (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="LMP datums" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Iestatīt uz šodienu
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Notīrīt rezultātus
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Jūsu paredzamais dzemdību datums:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Pašreizējais gestācijas vecums:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Bieži uzdotie jautājumi par grūtniecības kalkulatoriem</h2>

        <div class="faq-item">
          <div class="faq-question">Cik precīzs ir šis grūtniecības kalkulators?</div>
          <div class="faq-answer">Mūsu grūtniecības kalkulators izmanto standarta medicīnas formulu, pievienojot 280 dienas jūsu pēdējai menstruācijai. Šī metode ir aptuveni 95% precīza dzemdību datuma prognozēšanai, lai gan individuālas grūtniecības var atšķirties līdz pat divām nedēļām.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Ko darīt, ja neatceros precīzo LMP datumu?</div>
          <div class="faq-answer">Ja neatceraties precīzo LMP datumu, mēģiniet novērtēt pēc iespējas precīzāk. Jūsu veselības aprūpes sniedzējs var izmantot ultraskaņas mērījumus, lai nodrošinātu precīzāku dzemdību datumu pirmās prenatālās vizītes laikā.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Vai varu izmantot šo kalkulatoru, ja man ir neregulāras menstruācijas?
          </div>
          <div class="faq-answer">Ja jums ir neregulāri menstruāciju cikli, šis kalkulators var būt mazāk precīzs. Šādos gadījumos jūsu ārsts, iespējams, izmantos ultraskaņas datēšanu, lai precīzāk noteiktu dzemdību datumu.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Kad man jāplāno pirmā prenatālā vizīte?
          </div>
          <div class="faq-answer">
            Lielākā daļa veselības aprūpes sniedzēju iesaka plānot pirmo prenatālo vizīti 6-8 grūtniecības nedēļu vecumā. Izmantojiet mūsu kalkulatoru, lai noteiktu pašreizējo gestācijas vecumu un attiecīgi plānotu.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Kāda ir atšķirība starp gestācijas vecumu un augļa vecumu?
          </div>
          <div class="faq-answer">Gestācijas vecums tiek aprēķināts no pēdējās menstruācijas, bet augļa vecums tiek aprēķināts no apaugļošanas (parasti 2 nedēļas vēlāk). Medicīnas speciālisti parasti izmanto gestācijas vecumu konsistences dēļ.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
