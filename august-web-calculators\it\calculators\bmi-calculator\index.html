<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Calcolatore BMI per Adulti - Strumento BMI Preciso | MeetAugust</title>
    <meta
      name="description"
      content="Calcola il tuo BMI con il nostro strumento avanzato. Ottieni risultati immediati e scopri la tua categoria di peso. Gratuito, preciso e basato sulla scienza."
    />
    <meta
      name="keywords"
      content="calcolatore bmi, indice di massa corporea, categoria peso, calcolatore salute"
    />
    <meta name="author" content="MeetAugust" />
    <meta
      property="og:title"
      content="Calcolatore BMI per Adulti - Strumento BMI Preciso"
    />
    <meta
      property="og:description"
      content="Calcola il tuo BMI e scopri subito la tua categoria di peso."
    />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Calcolatore BMI per Adulti - Strumento BMI Preciso"
    />
    <meta
      name="twitter:description"
      content="Calcola il tuo BMI e scopri subito la tua categoria di peso."
    />
    <link rel="canonical" href="https://www.meetaugust.ai/it/calculators/bmi-calculator" />
    <link rel="icon" href="/it/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/it/calculators/bmi-calculator/style.css">
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
        <div class="navbar">
          <div class="logo">
            <a href="/it/calculators/">
                <img
              width="200"
              src="/it/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo Calcolatore"
            />
            </a>
          </div>
          <div class="nav">
            <div class="language-switcher" id="language-switcher">
              <select id="language-select">
                <!-- Options will be populated by JS -->
              </select>
            </div>
            <a
              href="https://app.meetaugust.ai/join/app?utm=bmi_calculator_topnav"
              class="talk-to-august"
              >Parla con August</a
            >
          </div>
        </div>
      </header>
        <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">Calcolatore BMI per Adulti</h1>
          <div class="page-info">
            <div class="info-badge">
              <div class="shield-icon">✓</div>
              <span>Per tutti</span>
            </div>
          </div>
        </div>

        <div class="content-grid">
          <div class="info-section">
            <h2>Comprendere il BMI per Adulti: Cosa Devi Sapere</h2>
            <p class="info-text">
              Il calcolatore BMI per adulti è uno strumento affidabile per valutare se il tuo peso è appropriato per la tua altezza. ...
            </p>

            <img
              src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BMI Scale"
              class="weight-image"
            />

            <div style="margin-top: 30px">
              <h3
                style="
                  color: var(--primary-color);
                  font-size: 20px;
                  margin-bottom: 15px;
                "
              >
                Calcolatore BMI gratuito per adulti (20+ anni)
              </h3>
              <p style="color: var(--text-secondary); margin-bottom: 15px">
                Usa questo calcolatore BMI per adulti per determinare immediatamente il tuo BMI e vedere in quale categoria di peso rientri — sottopeso, peso normale, sovrappeso o obesità. Questo calcolatore è progettato specificamente per adulti di età superiore ai 20 anni.
              </p>
              <!-- <p style="color: var(--text-secondary)">
                Il BMI è uno dei tanti strumenti utilizzati per valutare la salute generale e i potenziali rischi legati al peso. Deve essere interpretato insieme ad altre valutazioni cliniche come la storia medica, le abitudini di vita, i risultati dell'esame fisico e i test di laboratorio. -->
                <!-- <a href="#" style="color: var(--primary-color)">
                  Scopri di più sull'Indice di Massa Corporea
                </a>
                e come si inserisce nel tuo profilo di salute. Consulta sempre un operatore sanitario per consigli personalizzati — questo strumento è solo a scopo educativo e non sostituisce il parere medico.
              </p> -->
            </div>
          </div>

          <div class="calculator-section">
            <div style="display: flex; justify-content: space-between;" >

              <h3 class="calculator-title">Calcolatore BMI</h3>
              <button class="download-btn  neon-pulse" style="margin-left: 2rem; margin-top: 0; display: none; align-self: flex-start; font-size: 1.15rem; padding: 1rem 2rem;" id="download-pdf-btn" onclick="downloadBMIResultsPDF()"><span style="font-size:1.5em;vertical-align:middle;">📄</span> Download BMI Results</button>
            </div>

            <div class="unit-selector">
              <div class="unit-option">
                <input
                  type="radio"
                  id="us-units"
                  name="units"
                  value="us"
                  checked
                />
                <label for="us-units">Unità USA</label>
              </div>
              <div class="unit-option">
                <input
                  type="radio"
                  id="metric-units"
                  name="units"
                  value="metric"
                />
                <label for="metric-units">Unità Metriche</label>
              </div>
              <span class="reset-link" onclick="resetCalculator()">RESET</span>
            </div>

            <div class="form-group">
              <label class="form-label">ALTEZZA</label>
              <div class="input-group" id="height-inputs">
                <input
                  type="number"
                  class="form-input"
                  id="height-feet"
                  placeholder="0"
                  min="0"
                  max="10"
                />
                <span class="unit-label">piedi (ft)</span>
                <input
                  type="number"
                  class="form-input"
                  id="height-inches"
                  placeholder="0"
                  min="0"
                  max="11"
                />
                <span class="unit-label">pollici (in)</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">PESO</label>
              <div class="input-group">
                <input
                  type="number"
                  class="form-input"
                  id="weight"
                  placeholder="0"
                  min="0"
                />
                <span class="unit-label" id="weight-unit">libbre (lbs)</span>
              </div>
            </div>

            <button class="calculate-btn" onclick="calculateBMI()">
              Calcola
            </button>
          </div>
        </div>

        <div class="note-section">
          <p>
            <strong>Nota:</strong> Questo calcolatore BMI richiede JavaScript per funzionare correttamente. Se il tuo browser ha JavaScript disabilitato o riscontri problemi, puoi calcolare manualmente il tuo BMI con questa formula:
            <em>BMI = (peso in chilogrammi) / (altezza in metri × altezza in metri)</em>
            Ad esempio, se pesi 70 kg e sei alto 1,75 m, il tuo BMI è 22,9.
          </p>
        </div>
      </main>

<!-- Result section moved below main content -->
<div class="result-section-wrapper">
  <div class="result-section" id="result-section">
    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
      <div>
        <div class="bmi-value" id="bmi-value">0.0</div>
        <div class="bmi-category" id="bmi-category"></div>
        <div class="bmi-description" id="bmi-description"></div>
      </div>
    </div>
  </div>
</div>

    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="/it/calculators/bmi-calculator/script.js"></script>
    <script>
      const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
      const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
      const select = document.getElementById('language-select');
      const currentLang = window.location.pathname.split('/')[1] || 'en';
      locales.forEach(l => {
        const opt = document.createElement('option');
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener('change', function() {
        const newLang = this.value;
        let path = window.location.pathname.split('/');
        if (locales.includes(path[1])) { path[1] = newLang; }
        else { path = ['', newLang, ...path.slice(1)]; }
        localStorage.setItem('preferredLang', newLang);
        window.location.pathname = path.join('/');
      });
      // Persist language choice
      if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
        select.value = localStorage.getItem('preferredLang');
      }
    </script>
  </body>
</html> 