<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Calculateur BMI pour adultes – Outil BMI précis | MeetAugust</title>
    <meta
      name="description"
      content="Calculez votre BMI avec notre outil avancé. Obtenez des résultats immédiats et voyez votre catégorie de poids. Gratuit, précis et basé sur la science."
    />
    <meta
      name="keywords"
      content="calculateur bmi, indice de masse corporelle, catégorie de poids, calculateur santé"
    />
    <meta name="author" content="MeetAugust" />
    <meta
      property="og:title"
      content="Calculateur BMI pour adultes – Outil BMI précis"
    />
    <meta
      property="og:description"
      content="Calculez votre BMI et voyez immédiatement votre catégorie de poids."
    />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Calculateur BMI pour adultes – Outil BMI précis"
    />
    <meta
      name="twitter:description"
      content="Calculez votre BMI et voyez immédiatement votre catégorie de poids."
    />
    <link rel="canonical" href="https://www.meetaugust.ai/fr/calculators/bmi-calculator" />
    <link rel="icon" href="/fr/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/fr/calculators/bmi-calculator/style.css">
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
        <div class="navbar">
          <div class="logo">
            <a href="/fr/calculators/">
                <img
              width="200"
              src="/fr/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo du Calculateur"
            />
            </a>
          </div>
          <div class="nav">
            <div class="language-switcher" id="language-switcher">
              <select id="language-select">
                <!-- Options will be populated by JS -->
              </select>
            </div>
            <a
              href="https://app.meetaugust.ai/join/app?utm=bmi_calculator_topnav"
              class="talk-to-august"
              >Parler à August</a
            >
          </div>
        </div>
      </header>
        <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">Calculateur BMI pour adultes</h1>
          <div class="page-info">
            <div class="info-badge">
              <div class="shield-icon">✓</div>
              <span>Pour tous</span>
            </div>
          </div>
        </div>

        <div class="content-grid">
          <div class="info-section">
            <h2>Comprendre le BMI pour adultes : Ce qu'il faut savoir</h2>
            <p class="info-text">
              Le calculateur BMI pour adultes est un outil fiable pour évaluer si votre poids est approprié pour votre taille. ...
            </p>

            <img
              src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BMI Scale"
              class="weight-image"
            />

            <div style="margin-top: 30px">
              <h3
                style="
                  color: var(--primary-color);
                  font-size: 20px;
                  margin-bottom: 15px;
                "
              >
                Calculateur gratuit de BMI pour adultes (20+ ans)
              </h3>
              <p style="color: var(--text-secondary); margin-bottom: 15px">
                Utilisez ce calculateur de BMI pour adultes pour déterminer instantanément votre BMI et voir dans quelle catégorie de poids vous vous situez — insuffisance pondérale, poids normal, surpoids ou obésité. Cet outil est spécialement conçu pour les adultes de plus de 20 ans.
              </p>
              <!-- <p style="color: var(--text-secondary)">
                Le BMI est l'un des nombreux outils utilisés pour évaluer la santé globale et les risques potentiels liés au poids. Il doit être interprété avec d'autres évaluations cliniques telles que l'historique médical, les habitudes de vie, l'examen physique et les analyses de laboratoire. -->
                <!-- <a href="#" style="color: var(--primary-color)">
                  En savoir plus sur l'Indice de Masse Corporelle
                </a>
                et comment il s'intègre à votre profil de santé. Consultez toujours un professionnel de santé pour des conseils personnalisés — cet outil est à but éducatif uniquement et ne remplace pas un avis médical.
              </p> -->
            </div>
          </div>

          <div class="calculator-section">
            <div style="display: flex; justify-content: space-between;" >

              <h3 class="calculator-title">Calculateur IMC (BMI)</h3>
              <button class="download-btn  neon-pulse" style="margin-left: 2rem; margin-top: 0; display: none; align-self: flex-start; font-size: 1.15rem; padding: 1rem 2rem;" id="download-pdf-btn" onclick="downloadBMIResultsPDF()"><span style="font-size:1.5em;vertical-align:middle;">📄</span> Download BMI Results</button>
            </div>

            <div class="unit-selector">
              <div class="unit-option">
                <input
                  type="radio"
                  id="us-units"
                  name="units"
                  value="us"
                  checked
                />
                <label for="us-units">Unités US</label>
              </div>
              <div class="unit-option">
                <input
                  type="radio"
                  id="metric-units"
                  name="units"
                  value="metric"
                />
                <label for="metric-units">Unités Métriques</label>
              </div>
              <span class="reset-link" onclick="resetCalculator()">RÉINITIALISER</span>
            </div>

            <div class="form-group">
              <label class="form-label">TAILLE</label>
              <div class="input-group" id="height-inputs">
                <input
                  type="number"
                  class="form-input"
                  id="height-feet"
                  placeholder="0"
                  min="0"
                  max="10"
                />
                <span class="unit-label">pieds (ft)</span>
                <input
                  type="number"
                  class="form-input"
                  id="height-inches"
                  placeholder="0"
                  min="0"
                  max="11"
                />
                <span class="unit-label">pouces (in)</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">POIDS</label>
              <div class="input-group">
                <input
                  type="number"
                  class="form-input"
                  id="weight"
                  placeholder="0"
                  min="0"
                />
                <span class="unit-label" id="weight-unit">livres (lbs)</span>
              </div>
            </div>

            <button class="calculate-btn" onclick="calculateBMI()">
              Calculer
            </button>
          </div>
        </div>

        <div class="note-section">
          <p>
            <strong>Note :</strong> Ce calculateur de BMI nécessite JavaScript pour fonctionner correctement. Si votre navigateur a JavaScript désactivé ou si vous rencontrez des problèmes, vous pouvez calculer votre BMI manuellement avec cette formule :
            <em>BMI = (poids en kilogrammes) / (taille en mètres × taille en mètres)</em>
            Par exemple, si vous pesez 70 kg et mesurez 1,75 m, votre BMI est de 22,9.
          </p>
        </div>
      </main>

<!-- Result section moved below main content -->
<div class="result-section-wrapper">
  <div class="result-section" id="result-section">
    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
      <div>
        <div class="bmi-value" id="bmi-value">0.0</div>
        <div class="bmi-category" id="bmi-category"></div>
        <div class="bmi-description" id="bmi-description"></div>
      </div>
    </div>
  </div>
</div>

    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="/fr/calculators/bmi-calculator/script.js"></script>
    <script>
      const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
      const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
      const select = document.getElementById('language-select');
      const currentLang = window.location.pathname.split('/')[1] || 'en';
      locales.forEach(l => {
        const opt = document.createElement('option');
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener('change', function() {
        const newLang = this.value;
        let path = window.location.pathname.split('/');
        if (locales.includes(path[1])) { path[1] = newLang; }
        else { path = ['', newLang, ...path.slice(1)]; }
        localStorage.setItem('preferredLang', newLang);
        window.location.pathname = path.join('/');
      });
      // Persist language choice
      if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
        select.value = localStorage.getItem('preferredLang');
      }
    </script>
  </body>
</html> 