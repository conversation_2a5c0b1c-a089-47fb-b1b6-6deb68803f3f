<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calculadora de Gravidez Grátis - Calculadora de Data Provável do Parto e Idade Gestacional 2025</title>
    <meta name="description" content="Calcule a data provável do parto e idade gestacional com nossa calculadora de gravidez gratuita e precisa. Acompanhe sua jornada da gravidez semana a semana baseada na sua última menstruação." />
    <meta name="keywords" content="calculadora de gravidez, calculadora de data de parto, calculadora de idade gestacional, rastreador de gravidez, semanas de gravidez, calculadora DUM, data provável do parto, gestantes" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Calculadora de Gravidez Grátis - Calculadora de Data Provável do Parto e Idade Gestacional" />
    <meta property="og:description" content="Calcule a data provável do parto e idade gestacional com nossa calculadora de gravidez gratuita e precisa. Acompanhe sua jornada da gravidez semana a semana." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/pt/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/pt/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/pt/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/pt/calculators/">
            <img
              width="200"
              src="/pt/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo da Calculadora"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Fale com August</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Calculadora de Data de Parto</h1>
        <p>Estime a data de parto do seu bebê com base no seu último período menstrual ou na data de concepção. Use nossa calculadora de data de parto para planejar sua gravidez e se preparar para a chegada do seu bebê.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Confiável por gestantes e profissionais de saúde em todo o mundo</span>
          </div>

          <h2>Por que usar nossa Calculadora de Gravidez?</h2>
          <p>Nossa calculadora de gravidez gratuita ajuda você a determinar a data provável do parto do seu bebê e idade gestacional atual baseada na sua última menstruação (DUM). Seja você recém-grávida ou acompanhando sua jornada da gravidez, esta ferramenta fornece cálculos precisos para ajudar você a planejar a chegada do seu bebê.</p>

          <h3>Principais características:</h3>
          <ul>
            <li>Cálculo instantâneo da data provável do parto baseado na DUM</li>
            <li>Idade gestacional atual em semanas e dias</li>
            <li>Interface fácil de usar com menus suspensos</li>
            <li>Cálculos medicamente precisos seguindo diretrizes da OMS</li>
            <li>Gratuito para usar sem necessidade de registro</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Calcule sua Data Provável do Parto</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Data de Hoje:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Data atual" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Primeiro Dia da Última Menstruação (DUM):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Data da DUM" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Definir para Hoje
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Limpar Resultados
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Sua Data Provável do Parto:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Idade Gestacional Atual:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Perguntas Frequentes sobre Calculadoras de Gravidez</h2>

        <div class="faq-item">
          <div class="faq-question">Quão precisa é esta calculadora de gravidez?</div>
          <div class="faq-answer">Nossa calculadora de gravidez usa a fórmula médica padrão de adicionar 280 dias à sua última menstruação. Este método é aproximadamente 95% preciso para prever sua data provável do parto, embora gestações individuais possam variar até duas semanas.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">E se eu não me lembrar da data exata da minha DUM?</div>
          <div class="faq-answer">Se você não conseguir lembrar da data exata da sua DUM, tente estimar o mais próximo possível. Seu profissional de saúde pode usar medições de ultrassom para fornecer uma data provável do parto mais precisa durante sua primeira consulta pré-natal.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Posso usar esta calculadora se tenho períodos irregulares?
          </div>
          <div class="faq-answer">Se você tem ciclos menstruais irregulares, esta calculadora pode ser menos precisa. Nesses casos, seu médico provavelmente usará datação por ultrassom para determinar sua data provável do parto com mais precisão.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Quando devo agendar minha primeira consulta pré-natal?
          </div>
          <div class="faq-answer">
            A maioria dos profissionais de saúde recomenda agendar sua primeira consulta pré-natal entre 6-8 semanas de gravidez. Use nossa calculadora para determinar sua idade gestacional atual e planejar adequadamente.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Qual é a diferença entre idade gestacional e idade fetal?
          </div>
          <div class="faq-answer">A idade gestacional é calculada a partir da sua última menstruação, enquanto a idade fetal é calculada a partir da concepção (tipicamente 2 semanas depois). Profissionais médicos tipicamente usam idade gestacional para consistência.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
