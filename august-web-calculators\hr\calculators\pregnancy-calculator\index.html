<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Besplatni Kalkulator Trudnoće - Kalkulator Termina Poroda i Gestacijske Dobi 2025</title>
    <meta name="description" content="Izračunajte termin poroda i gestacijsku dob s našim besplatnim, točnim kalkulatorom trudnoće. Pratite svoju trudnoću tjedan za tjednom na osnovu posljednje menstruacije." />
    <meta name="keywords" content="kalkulator trudnoće, kalkulator termina poroda, kalkulator gestacijske dobi, praćenje trudno<PERSON>e, tjedni trudno<PERSON>, kalkulator posljednje menstruacije, termin poroda, buduće majke" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Besplatni Kalkulator Trudnoće - Kalkulator Termina Poroda i Gestacijske Dobi" />
    <meta property="og:description" content="Izračunajte termin poroda i gestacijsku dob s našim besplatnim, točnim kalkulatorom trudnoće. Pratite svoju trudnoću tjedan za tjednom." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/hr/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/hr/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/hr/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/hr/calculators/">
            <img
              width="200"
              src="/hr/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo Kalkulatora"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Razgovarajte s Augustom</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Besplatni Kalkulator Trudnoće i Kalkulator Termina Poroda</h1>
        <p>Izračunajte termin poroda i pratite gestacijsku dob s našim točnim, jednostavnim kalkulatorom trudnoće</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Pouzdaju se buduće majke i zdravstveni profesionalci širom svijeta</span>
          </div>

          <h2>Zašto Koristiti Naš Kalkulator Trudnoće?</h2>
          <p>Naš besplatni kalkulator trudnoće pomaže vam odrediti termin poroda vašeg djeteta i trenutnu gestacijsku dob na osnovu posljednje menstruacije (LMP). Bilo da ste tek trudni ili pratite svoju trudnoću, ovaj alat pruža točne izračune koji vam pomažu planirati dolazak vašeg djeteta.</p>

          <h3>Ključne Značajke:</h3>
          <ul>
            <li>Trenutni izračun termina poroda na osnovu LMP</li>
            <li>Trenutna gestacijska dob u tjednima i danima</li>
            <li>Jednostavno sučelje s padajućim izbornicima</li>
            <li>Medicinski točni izračuni prema WHO smjernicama</li>
            <li>Besplatno za korištenje bez potrebe za registracijom</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Izračunajte Svoj Termin Poroda</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Današnji Datum:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Trenutni datum" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Prvi Dan Posljednje Menstruacije (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="LMP datum" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Postavi na Danas
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Obriši Rezultate
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Vaš Procijenjeni Termin Poroda:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Trenutna Gestacijska Dob:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Često Postavljana Pitanja o Kalkulatorima Trudnoće</h2>

        <div class="faq-item">
          <div class="faq-question">Koliko je točan ovaj kalkulator trudnoće?</div>
          <div class="faq-answer">Naš kalkulator trudnoće koristi standardnu medicinsku formulu dodavanja 280 dana vašoj posljednjoj menstruaciji. Ova metoda je približno 95% točna za predviđanje termina poroda, iako se individualne trudnoće mogu razlikovati do dva tjedna.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Što ako se ne sjećam točnog datuma LMP?</div>
          <div class="faq-answer">Ako se ne možete sjetiti točnog datuma LMP, pokušajte procijeniti što je točnije moguće. Vaš zdravstveni pružatelj može koristiti ultrazvučne mjerenja za precizniji termin poroda tijekom prvog prenatalnog posjeta.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Mogu li koristiti ovaj kalkulator ako imam nepravilne cikluse?
          </div>
          <div class="faq-answer">Ako imate nepravilne menstrualne cikluse, ovaj kalkulator može biti manje točan. U takvim slučajevima, vaš liječnik će vjerojatno koristiti ultrazvučno datiranje za precizniji termin poroda.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Kada trebam zakazati prvi prenatalni pregled?
          </div>
          <div class="faq-answer">
            Većina zdravstvenih pružatelja preporučuje zakazivanje prvog prenatalnog pregleda između 6-8 tjedana trudnoće. Koristite naš kalkulator za određivanje trenutne gestacijske dobi i planirajte u skladu s tim.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Koja je razlika između gestacijske dobi i fetalne dobi?
          </div>
          <div class="faq-answer">Gestacijska dob se računa od posljednje menstruacije, dok se fetalna dob računa od začeća (obično 2 tjedna kasnije). Medicinski profesionalci obično koriste gestacijsku dob radi dosljednosti.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
