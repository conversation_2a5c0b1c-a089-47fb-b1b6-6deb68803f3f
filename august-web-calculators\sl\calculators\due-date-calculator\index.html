<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Brezplačni Kalkulator Nosečnosti - Kalkulator Roka Poroda in Gestacijske Starosti 2025</title>
    <meta name="description" content="Izračunajte rok poroda in gestacijsko starost z našim brezplačnim in natančnim kalkulatorjem nosečnosti. Spremljajte svojo nosečnost teden za tednom na podlagi zadnje menstruacije." />
    <meta name="keywords" content="kalkulator nosečnosti, kalkulator roka poroda, kalkulator gestacijske starosti, sledenje nosečnosti, tedni nosečnosti, kalkulator LMP, rok poroda, bodoče mamice" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Brezplačni Kalkulator Nosečnosti - Kalkulator Roka Poroda in Gestacijske Starosti" />
    <meta property="og:description" content="Izračunajte rok poroda in gestacijsko starost z našim brezplačnim in natančnim kalkulatorjem nosečnosti. Spremljajte svojo nosečnost teden za tednom." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/sl/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/sl/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/sl/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/sl/calculators/">
            <img
              width="200"
              src="/sl/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logotip kalkulatorja"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Pogovorite se z Augustom</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Kalkulator roka poroda</h1>
        <p>Ocenite rok poroda vašega otroka na podlagi zadnje menstruacije ali datuma spočetja. Uporabite naš kalkulator roka poroda za načrtovanje nosečnosti in pripravo na prihod vašega otroka.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Zaupajo mu bodoče mamice in zdravstveni delavci po vsem svetu</span>
          </div>

          <h2>Zakaj uporabljati naš kalkulator nosečnosti?</h2>
          <p>Naš brezplačni kalkulator nosečnosti vam pomaga določiti rok poroda vašega otroka in trenutno gestacijsko starost na podlagi zadnje menstruacije (LMP). Ne glede na to, ali ste pravkar zanosili ali spremljate svojo nosečnost, to orodje zagotavlja natančne izračune za lažje načrtovanje prihoda vašega otroka.</p>

          <h3>Ključne funkcije:</h3>
          <ul>
            <li>Takojšnji izračun roka poroda na podlagi LMP</li>
            <li>Trenutna gestacijska starost v tednih in dneh</li>
            <li>Enostaven vmesnik z izbirnimi meniji</li>
            <li>Medicinsko natančni izračuni v skladu s smernicami WHO</li>
            <li>Brezplačno za uporabo brez registracije</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Izračunajte rok poroda</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Današnji datum:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Trenutni datum" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Prvi dan zadnje menstruacije (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Datum LMP" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Nastavi na danes
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Počisti rezultate
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Vaš predviden rok poroda:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Trenutna gestacijska starost:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Pogosto zastavljena vprašanja o kalkulatorjih nosečnosti</h2>

        <div class="faq-item">
          <div class="faq-question">Kako natančen je ta kalkulator nosečnosti?</div>
          <div class="faq-answer">Naš kalkulator nosečnosti uporablja standardno medicinsko formulo, ki doda 280 dni k vaši zadnji menstruaciji. Ta metoda je približno 95 % natančna pri napovedovanju roka poroda, čeprav se posamezne nosečnosti lahko razlikujejo do dva tedna.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Kaj če se ne spomnim točnega datuma moje zadnje menstruacije?</div>
          <div class="faq-answer">Če se ne spomnite točnega datuma vaše zadnje menstruacije, poskusite oceniti čim bolj natančno. Vaš zdravnik lahko uporabi ultrazvočne meritve, da določi natančnejši rok poroda med vašim prvim predporodnim obiskom.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Ali lahko uporabljam ta kalkulator, če imam nepravilne menstruacije?
          </div>
          <div class="faq-answer">Če imate nepravilne menstrualne cikluse, je ta kalkulator morda manj natančen. V takih primerih bo vaš zdravnik verjetno uporabil ultrazvočno datiranje, da natančneje določi rok poroda.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Kdaj naj načrtujem svoj prvi predporodni obisk?
          </div>
          <div class="faq-answer">
            Večina zdravnikov priporoča, da prvi predporodni obisk načrtujete med 6. in 8. tednom nosečnosti. Uporabite naš kalkulator, da določite svojo trenutno gestacijsko starost in ustrezno načrtujete.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Kakšna je razlika med gestacijsko starostjo in fetalno starostjo?
          </div>
          <div class="faq-answer">Gestacijska starost se izračuna od vaše zadnje menstruacije, medtem ko se fetalna starost izračuna od spočetja (običajno 2 tedna kasneje). Zdravstveni delavci običajno uporabljajo gestacijsko starost za večjo doslednost.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
