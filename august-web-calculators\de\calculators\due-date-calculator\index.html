<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kostenloser Schwangerschaftsrechner - Entbindungstermin- und Gestationsalter-Rechner 2025</title>
    <meta name="description" content="Berechnen Sie Ihren Entbindungstermin und Ihr Gestationsalter mit unserem kostenlosen, genauen Schwangerschaftsrechner. Verfolgen Sie Ihre Schwangerschaft Woche für Woche basierend auf Ihrer letzten Menstruationsperiode." />
    <meta name="keywords" content="Schwangerschaftsrechner, Entbindungsterminrechner, Gestationsalterrechner, Schwangerschaftsverfolgung, Schwangerschaftswochen, LMP-Rechner, Entbindungstermin, werdende Mütter" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Kostenloser Schwangerschaftsrechner - Entbindungstermin- und Gestationsalter-Rechner" />
    <meta property="og:description" content="Berechnen Sie Ihren Entbindungstermin und Ihr Gestationsalter mit unserem kostenlosen, genauen Schwangerschaftsrechner. Verfolgen Sie Ihre Schwangerschaft Woche für Woche." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/de/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/de/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/de/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/de/calculators/">
            <img
              width="200"
              src="/de/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Rechner Logo"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Sprechen Sie mit August</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Geburtsterminsrechner</h1>
        <p>Schätzen Sie den Geburtstermin Ihres Babys basierend auf Ihrer letzten Menstruationsperiode oder dem Empfängnisdatum. Verwenden Sie unseren Geburtsterminsrechner, um Ihre Schwangerschaft zu planen und sich auf die Ankunft Ihres Babys vorzubereiten.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Von werdenden Müttern und medizinischen Fachkräften weltweit vertraut</span>
          </div>

          <h2>Warum unseren Schwangerschaftsrechner verwenden?</h2>
          <p>Unser kostenloser Schwangerschaftsrechner hilft Ihnen, den Entbindungstermin Ihres Babys und Ihr aktuelles Gestationsalter basierend auf Ihrer letzten Menstruationsperiode (LMP) zu bestimmen. Egal, ob Sie gerade schwanger geworden sind oder Ihre Schwangerschaft verfolgen, dieses Tool bietet genaue Berechnungen, um die Ankunft Ihres Babys zu planen.</p>

          <h3>Hauptmerkmale:</h3>
          <ul>
            <li>Sofortige Berechnung des Entbindungstermins basierend auf der LMP</li>
            <li>Aktuelles Gestationsalter in Wochen und Tagen</li>
            <li>Benutzerfreundliche Oberfläche mit Dropdown-Menüs</li>
            <li>Medizinisch genaue Berechnungen nach WHO-Richtlinien</li>
            <li>Kostenlos ohne Registrierung nutzbar</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Berechnen Sie Ihren Entbindungstermin</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Heutiges Datum:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Aktuelles Datum" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Erster Tag der letzten Menstruationsperiode (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="LMP-Datum" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Auf heute setzen
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Ergebnisse löschen
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Ihr geschätzter Entbindungstermin:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Aktuelles Gestationsalter:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Häufig gestellte Fragen zu Schwangerschaftsrechnern</h2>

        <div class="faq-item">
          <div class="faq-question">Wie genau ist dieser Schwangerschaftsrechner?</div>
          <div class="faq-answer">Unser Schwantertschaftsrechner verwendet die standardmäßige medizinische Formel, indem 280 Tage zur letzten Menstruationsperiode addiert werden. Diese Methode ist etwa 95 % genau bei der Vorhersage des Entbindungstermins, wobei individuelle Schwangerschaften bis zu zwei Wochen variieren können.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Was ist, wenn ich mich nicht an das genaue Datum meiner LMP erinnere?</div>
          <div class="faq-answer">Wenn Sie sich nicht an das genaue Datum Ihrer LMP erinnern, versuchen Sie, so genau wie möglich zu schätzen. Ihr Arzt kann Ultraschallmessungen verwenden, um bei Ihrem ersten pränatalen Besuch einen genaueren Entbindungstermin zu bestimmen.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Kann ich diesen Rechner verwenden, wenn ich unregelmäßige Perioden habe?
          </div>
          <div class="faq-answer">Wenn Sie unregelmäßige Menstruationszyklen haben, kann dieser Rechner weniger genau sein. In solchen Fällen wird Ihr Arzt wahrscheinlich Ultraschalldatierungen verwenden, um den Entbindungstermin präziser zu bestimmen.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Wann sollte ich meinen ersten pränatalen Termin vereinbaren?
          </div>
          <div class="faq-answer">
            Die meisten Ärzte empfehlen, den ersten pränatalen Termin zwischen der 6. und 8. Schwangerschaftswoche zu vereinbaren. Verwenden Sie unseren Rechner, um Ihr aktuelles Gestationsalter zu bestimmen und entsprechend zu planen.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Was ist der Unterschied zwischen Gestationsalter und fötalem Alter?
          </div>
          <div class="faq-answer">Das Gestationsalter wird ab Ihrer letzten Menstruationsperiode berechnet, während das fötale Alter ab der Empfängnis berechnet wird (in der Regel 2 Wochen später). Medizinische Fachkräfte verwenden in der Regel das Gestationsalter für Konsistenz.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
