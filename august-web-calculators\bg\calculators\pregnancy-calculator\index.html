<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Безплатен Калкулатор за Бременност - Калкулатор за Дата на Раждане и Гестационна Възраст 2025</title>
    <meta name="description" content="Изчислете датата на раждане и гестационната възраст с нашия безплатен и точен калкулатор за бременност. Проследявайте бременността си седмица по седмица въз основа на последния ви менструален период." />
    <meta name="keywords" content="калкулатор за бременност, калкулатор за дата на раждане, калкулатор за гестационна възраст, проследяване на бременност, седмици на бременност, калкулатор за ЛМП, дата на раждане, бъдещи майки" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Безплатен Калкулатор за Бременност - Калкулатор за Дата на Раждане и Гестационна Възраст" />
    <meta property="og:description" content="Изчислете датата на раждане и гестационната възраст с нашия безплатен и точен калкулатор за бременност. Проследявайте бременността си седмица по седмица." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/bg/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/bg/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/bg/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/bg/calculators/">
            <img
              width="200"
              src="/bg/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Лого на калкулатора"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Говори с Август</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Безплатен Калкулатор за Бременност и Калкулатор за Дата на Раждане</h1>
        <p>Изчислете датата на раждане и проследявайте гестационната възраст с нашия точен и лесен за употреба калкулатор за бременност.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Доверен от бъдещи майки и здравни специалисти по целия свят</span>
          </div>

          <h2>Защо да използвате нашия калкулатор за бременност?</h2>
          <p>Нашият безплатен калкулатор за бременност ви помага да определите датата на раждане на вашето бебе и текущата гестационна възраст въз основа на последния ви менструален период (ЛМП). Независимо дали сте наскоро бременна или проследявате бременността си, този инструмент предоставя точни изчисления, за да ви помогне да планирате пристигането на вашето бебе.</p>

          <h3>Основни характеристики:</h3>
          <ul>
            <li>Незабавно изчисляване на датата на раждане въз основа на ЛМП</li>
            <li>Текуща гестационна възраст в седмици и дни</li>
            <li>Лесен за употреба интерфейс с падащи менюта</li>
            <li>Медицински точни изчисления според насоките на СЗО</li>
            <li>Безплатен за употреба без регистрация</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Изчислете датата на раждане</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Днешна дата:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Текуща дата" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Първи ден от последния менструален период (ЛМП):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Дата на ЛМП" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Задайте на днес
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Изчисти резултатите
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Вашата прогнозна дата на раждане:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Текуща гестационна възраст:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Често задавани въпроси за калкулаторите за бременност</h2>

        <div class="faq-item">
          <div class="faq-question">Колко точен е този калкулатор за бременност?</div>
          <div class="faq-answer">Нашият калкулатор за бременност използва стандартната медицинска формула за добавяне на 280 дни към последния ви менструален период. Този метод е приблизително 95% точен за предвиждане на датата на раждане, въпреки че индивидуалните бременности могат да варират до две седмици.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Ами ако не помня точната дата на моя ЛМП?</div>
          <div class="faq-answer">Ако не помните точната дата на вашия ЛМП, опитайте се да оцените възможно най-точно. Вашият лекар може да използва ултразвукови измервания, за да предостави по-точна дата на раждане по време на първото ви пренатално посещение.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Мога ли да използвам този калкулатор, ако имам нередовни менструации?
          </div>
          <div class="faq-answer">Ако имате нередовни менструални цикли, този калкулатор може да бъде по-малко точен. В такива случаи вашият лекар вероятно ще използва ултразвуково датиране, за да определи по-точно датата на раждане.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Кога трябва да насроча първия си пренатален преглед?
          </div>
          <div class="faq-answer">
            Повечето лекари препоръчват да насрочите първия си пренатален преглед между 6 и 8 седмици от бременността. Използвайте нашия калкулатор, за да определите текущата си гестационна възраст и да планирате съответно.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Каква е разликата между гестационна възраст и фетална възраст?
          </div>
          <div class="faq-answer">Гестационната възраст се изчислява от последния ви менструален период, докато феталната възраст се изчислява от зачатието (обикновено 2 седмици по-късно). Медицинските специалисти обикновено използват гестационната възраст за по-голяма последователност.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
