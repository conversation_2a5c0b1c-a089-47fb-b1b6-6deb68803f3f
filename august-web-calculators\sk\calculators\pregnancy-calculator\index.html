<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bezplatná Tehotenská Kalkulačka - Kalkulačka Dátumu Pôrodu a Gestačného Veku 2025</title>
    <meta name="description" content="Vypočítajte si dátum pôrodu a gestačný vek s našou bezplatnou a presnou tehotenskou kalkulačkou. Sledujte svoje tehotenstvo týždeň po týždni na základe vašej poslednej menštruácie." />
    <meta name="keywords" content="tehotenská kalkulačka, kalkulačka dátumu pôrodu, kalkulačka gestačného veku, sledovanie tehotenstva, týždne tehotenstva, kalkulačka LMP, dátum pôrodu, bud<PERSON><PERSON> ma<PERSON>" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Bezplatná Tehotenská Kalkulačka - Kalkulačka Dátumu Pôrodu a Gestačného Veku" />
    <meta property="og:description" content="Vypočítajte si dátum pôrodu a gestačný vek s našou bezplatnou a presnou tehotenskou kalkulačkou. Sledujte svoje tehotenstvo týždeň po týždni." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/sk/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/sk/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/sk/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/sk/calculators/">
            <img
              width="200"
              src="/sk/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo kalkulačky"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Porozprávajte sa s Augustom</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Bezplatná Tehotenská Kalkulačka a Kalkulačka Dátumu Pôrodu</h1>
        <p>Vypočítajte si dátum pôrodu a sledujte gestačný vek s našou presnou a jednoducho použiteľnou tehotenskou kalkulačkou.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Dôverujú jej budúce mamičky a zdravotnícki pracovníci po celom svete</span>
          </div>

          <h2>Prečo používať našu tehotenskú kalkulačku?</h2>
          <p>Naša bezplatná tehotenská kalkulačka vám pomáha určiť dátum pôrodu vášho dieťaťa a aktuálny gestačný vek na základe vašej poslednej menštruácie (LMP). Či už ste čerstvo tehotná alebo sledujete svoje tehotenstvo, tento nástroj poskytuje presné výpočty na plánovanie príchodu vášho dieťaťa.</p>

          <h3>Kľúčové vlastnosti:</h3>
          <ul>
            <li>Okamžitý výpočet dátumu pôrodu na základe LMP</li>
            <li>Aktuálny gestačný vek v týždňoch a dňoch</li>
            <li>Jednoduché rozhranie s rozbaľovacími ponukami</li>
            <li>Medicínsky presné výpočty podľa usmernení WHO</li>
            <li>Bezplatné používanie bez potreby registrácie</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Vypočítajte si dátum pôrodu</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Dnešný dátum:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Aktuálny dátum" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Prvý deň poslednej menštruácie (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Dátum LMP" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Nastaviť na dnes
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Vymazať výsledky
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Váš odhadovaný dátum pôrodu:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Aktuálny gestačný vek:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Často kladené otázky o tehotenských kalkulačkách</h2>

        <div class="faq-item">
          <div class="faq-question">Ako presná je táto tehotenská kalkulačka?</div>
          <div class="faq-answer">Naša tehotenská kalkulačka používa štandardnú medicínsku formulu, ktorá pridáva 280 dní k vašej poslednej menštruácii. Táto metóda je približne 95 % presná pri predpovedaní dátumu pôrodu, hoci jednotlivé tehotenstvá sa môžu líšiť až o dva týždne.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Čo ak si nepamätám presný dátum mojej poslednej menštruácie?</div>
          <div class="faq-answer">Ak si nepamätáte presný dátum vašej poslednej menštruácie, pokúste sa odhadnúť čo najpresnejšie. Váš lekár môže použiť ultrazvukové merania na určenie presnejšieho dátumu pôrodu počas vašej prvej prenatálnej návštevy.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Môžem použiť túto kalkulačku, ak mám nepravidelnú menštruáciu?
          </div>
          <div class="faq-answer">Ak máte nepravidelné menštruačné cykly, táto kalkulačka môže byť menej presná. V takýchto prípadoch váš lekár pravdepodobne použije ultrazvukové datovanie na presnejšie určenie dátumu pôrodu.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Kedy by som si mala naplánovať svoju prvú prenatálnu návštevu?
          </div>
          <div class="faq-answer">
            Väčšina lekárov odporúča naplánovať prvú prenatálnu návštevu medzi 6. a 8. týždňom tehotenstva. Použite našu kalkulačku na určenie vášho aktuálneho gestačného veku a podľa toho plánujte.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Aký je rozdiel medzi gestačným vekom a fetálnym vekom?
          </div>
          <div class="faq-answer">Gestačný vek sa počíta od vašej poslednej menštruácie, zatiaľ čo fetálny vek sa počíta od počatia (zvyčajne o 2 týždne neskôr). Zdravotnícki pracovníci zvyčajne používajú gestačný vek pre väčšiu konzistentnosť.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
