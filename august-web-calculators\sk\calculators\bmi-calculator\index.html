<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Kalkulačka BMI pre dospelých – Presný nástroj BMI | MeetAugust</title>
    <meta
      name="description"
      content="Vypočítajte si svoj BMI pomocou nášho pokročilého nástroja. Získajte okamžité výsledky a zistite svoju hmotnostnú kategóriu. Zadarmo, presné a vedecky podložené."
    />
    <meta
      name="keywords"
      content="bmi kalkula<PERSON>ka, index telesnej hmotnosti, hmotnostná kategória, zdravotná kalkulačka"
    />
    <meta name="author" content="MeetAugust" />
    <meta
      property="og:title"
      content="Kalkulačka BMI pre dospelých – Presný nástroj BMI"
    />
    <meta
      property="og:description"
      content="Vypočítajte si svoj BMI a okamžite zistite svoju hmotnostnú kategóriu."
    />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Kalkulačka BMI pre dospelých – Presný nástroj BMI"
    />
    <meta
      name="twitter:description"
      content="Vypočítajte si svoj BMI a okamžite zistite svoju hmotnostnú kategóriu."
    />
    <link rel="canonical" href="https://www.meetaugust.ai/sk/calculators/bmi-calculator" />
    <link rel="icon" href="/sk/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/sk/calculators/bmi-calculator/style.css">
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
        <div class="navbar">
          <div class="logo">
            <a href="/sk/calculators/">
                <img
              width="200"
              src="/sk/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo kalkulačky"
            />
            </a>
          </div>
          <div class="nav">
            <div class="language-switcher" id="language-switcher">
              <select id="language-select">
                <!-- Options will be populated by JS -->
              </select>
            </div>
            <a
              href="https://app.meetaugust.ai/join/app?utm=bmi_calculator_topnav"
              class="talk-to-august"
              >Porozprávajte sa s Augustom</a
            >
          </div>
        </div>
      </header>
        <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">Kalkulačka BMI pre dospelých</h1>
          <div class="page-info">
            <div class="info-badge">
              <div class="shield-icon">✓</div>
              <span>Pre všetkých</span>
            </div>
          </div>
        </div>

        <div class="content-grid">
          <div class="info-section">
            <h2>Pochopenie BMI pre dospelých: Čo potrebujete vedieť</h2>
            <p class="info-text">
              Kalkulačka BMI pre dospelých je spoľahlivý nástroj na posúdenie, či je vaša hmotnosť primeraná vašej výške. ...
            </p>

            <img
              src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BMI Scale"
              class="weight-image"
            />

            <div style="margin-top: 30px">
              <h3
                style="
                  color: var(--primary-color);
                  font-size: 20px;
                  margin-bottom: 15px;
                "
              >
                Bezplatná kalkulačka BMI pre dospelých (20+ rokov)
              </h3>
              <p style="color: var(--text-secondary); margin-bottom: 15px">
                Použite túto kalkulačku BMI pre dospelých na okamžité určenie svojho BMI a zistenie, do ktorej hmotnostnej kategórie patríte — podváha, normálna hmotnosť, nadváha alebo obezita. Táto kalkulačka je určená špeciálne pre dospelých nad 20 rokov.
              </p>
              <!-- <p style="color: var(--text-secondary)">
                BMI je jedným z mnohých nástrojov na hodnotenie celkového zdravia a potenciálnych rizík súvisiacich s hmotnosťou. Mal by sa interpretovať spolu s ďalšími klinickými hodnoteniami, ako je anamnéza, životné návyky, výsledky fyzických vyšetrení a laboratórnych testov. -->
                <!-- <a href="#" style="color: var(--primary-color)">
                  Zistite viac o indexe telesnej hmotnosti
                </a>
                a ako zapadá do vášho zdravotného profilu. Vždy sa poraďte so zdravotníckym pracovníkom o osobnom poradenstve — tento nástroj je určený len na vzdelávacie účely a nenahrádza lekársku radu.
              </p> -->
            </div>
          </div>

          <div class="calculator-section">
            <div style="display: flex; justify-content: space-between;" >

              <h3 class="calculator-title">Kalkulačka BMI</h3>
              <button class="download-btn  neon-pulse" style="margin-left: 2rem; margin-top: 0; display: none; align-self: flex-start; font-size: 1.15rem; padding: 1rem 2rem;" id="download-pdf-btn" onclick="downloadBMIResultsPDF()"><span style="font-size:1.5em;vertical-align:middle;">📄</span> Download BMI Results</button>
            </div>

            <div class="unit-selector">
              <div class="unit-option">
                <input
                  type="radio"
                  id="us-units"
                  name="units"
                  value="us"
                  checked
                />
                <label for="us-units">Americké jednotky</label>
              </div>
              <div class="unit-option">
                <input
                  type="radio"
                  id="metric-units"
                  name="units"
                  value="metric"
                />
                <label for="metric-units">Metrické jednotky</label>
              </div>
              <span class="reset-link" onclick="resetCalculator()">RESETOVAŤ</span>
            </div>

            <div class="form-group">
              <label class="form-label">VÝŠKA</label>
              <div class="input-group" id="height-inputs">
                <input
                  type="number"
                  class="form-input"
                  id="height-feet"
                  placeholder="0"
                  min="0"
                  max="10"
                />
                <span class="unit-label">stopy (ft)</span>
                <input
                  type="number"
                  class="form-input"
                  id="height-inches"
                  placeholder="0"
                  min="0"
                  max="11"
                />
                <span class="unit-label">palce (in)</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">HMOTNOSŤ</label>
              <div class="input-group">
                <input
                  type="number"
                  class="form-input"
                  id="weight"
                  placeholder="0"
                  min="0"
                />
                <span class="unit-label" id="weight-unit">libry (lbs)</span>
              </div>
            </div>

            <button class="calculate-btn" onclick="calculateBMI()">
              Vypočítať
            </button>
          </div>
        </div>

        <div class="note-section">
          <p>
            <strong>Poznámka:</strong> Táto kalkulačka BMI vyžaduje JavaScript na správne fungovanie. Ak je vo vašom prehliadači JavaScript zakázaný alebo máte problémy, môžete si svoj index telesnej hmotnosti vypočítať ručne podľa tohto vzorca:
            <em>BMI = (hmotnosť v kilogramoch) / (výška v metroch × výška v metroch)</em>
            Napríklad, ak vážite 70 kg a meriate 1,75 m, váš BMI je 22,9.
          </p>
        </div>
      </main>

<!-- Result section moved below main content -->
<div class="result-section-wrapper">
  <div class="result-section" id="result-section">
    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
      <div>
        <div class="bmi-value" id="bmi-value">0.0</div>
        <div class="bmi-category" id="bmi-category"></div>
        <div class="bmi-description" id="bmi-description"></div>
      </div>
    </div>
  </div>
</div>

    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="/sk/calculators/bmi-calculator/script.js"></script>
    <script>
      const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
      const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
      const select = document.getElementById('language-select');
      const currentLang = window.location.pathname.split('/')[1] || 'en';
      locales.forEach(l => {
        const opt = document.createElement('option');
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener('change', function() {
        const newLang = this.value;
        let path = window.location.pathname.split('/');
        if (locales.includes(path[1])) { path[1] = newLang; }
        else { path = ['', newLang, ...path.slice(1)]; }
        localStorage.setItem('preferredLang', newLang);
        window.location.pathname = path.join('/');
      });
      // Persist language choice
      if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
        select.value = localStorage.getItem('preferredLang');
      }
    </script>
  </body>
</html> 