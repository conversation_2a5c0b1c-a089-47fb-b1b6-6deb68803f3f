
    :root {
        --primary-color: #206e55;
        --primary-dark: #1a5a47;
     --primary-light: #f0f9f4;
        --text-primary: #111827;
        --text-secondary: #374151;
        --background-light: #f8f9fa;
        --background-white: #fff;
        --border-radius: 18px;
        --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.10);
        --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.07);
        --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.04);
        /* Remove gradient for consistency */
      }
      body {
        font-family: "Work Sans", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: var(--primary-light);
        margin: 0;
        color: var(--text-primary);
        min-height: 100vh;
      }

    /* Header Styles */
    header {
      background-color: var(--background-white);
      padding: 12px 0;
      border-bottom: 1px solid var(--border-color);
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: var(--shadow-sm);
    }

    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      /* max-width: 1200px; */
      margin: 0 auto;
      padding: 0 20px;
    }

    .logo img {
      height: 50px;
      width: auto;
    }

    .nav {
      display: flex;
      align-items: center;
      gap: 24px;
    }

    .language-switcher select {
      border: 1px solid var(--border-color);
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 14px;
      color: var(--text-secondary);
      /* background: var(--background-white);.. */
      outline: none;
      cursor: pointer;
    }

    .talk-to-august {
      background-color: var(--primary-color);
      color: white;
      padding: 10px 20px;
      border-radius: 6px;
      text-decoration: none;
      font-weight: 600;
      font-size: 14px;
      transition: var(--transition);
    }

    .talk-to-august:hover {
      background-color: var(--primary-dark);
    }

    /* Main Container */
    .main-container {
      max-width: 1440px;
      margin: 0 auto;
      padding: 60px 40px 40px 40px;
    }

    .content-layout {
      display: grid;
      grid-template-columns: 1.2fr 1fr;
      gap: 80px;
      align-items: start;
    }

    /* Left Content */
    .content-section {
      background: var(--background-white);
      border-radius: var(--border-radius);
      padding: 56px 48px 48px 48px;
      box-shadow: var(--shadow-lg);
      min-height: 600px;
    }

    .content-header {
      text-align: left;
      margin-bottom: 36px;
    }

    .content-header h1 {
      font-family: 'Roboto Condensed', Arial, sans-serif;
      font-size: 2.8rem;
      color: var(--primary-color);
      margin-bottom: 18px;
      font-weight: 800;
      letter-spacing: -1px;
      text-align: left;
    }

    .content-description {
      font-size: 1.18rem;
      color: var(--text-secondary);
      max-width: 700px;
      margin: 0 0 18px 0;
      line-height: 1.8;
    }

    .cycle-facts {
      background: var(--primary-light);
      border-radius: 14px;
      padding: 24px 28px;
      margin-bottom: 32px;
      box-shadow: var(--shadow-sm);
    }

    .cycle-facts h3 {
      color: var(--primary-color);
      font-size: 1.25rem;
      margin-bottom: 10px;
      font-weight: 700;
    }

    .cycle-facts ul {
      margin: 0;
      padding-left: 20px;
      color: var(--text-secondary);
      font-size: 1.05rem;
      line-height: 1.7;
    }

    .motivational-quote {
      font-style: italic;
      color: #1a5a47;
      font-size: 1.1rem;
      margin: 24px 0 0 0;
      padding-left: 8px;
      border-left: 4px solid #206e55;
    }

    .features-list {
      margin-top: 36px;
      display: grid;
      gap: 28px;
    }

    .feature-item {
      display: flex;
      align-items: flex-start;
      gap: 18px;
      padding: 24px;
      background: var(--primary-light);
      border-radius: var(--border-radius);
      border-left: 5px solid var(--primary-color);
      box-shadow: var(--shadow-sm);
    }

    .feature-icon {
      width: 32px;
      height: 32px;
      background: var(--primary-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 18px;
      box-shadow: 0 2px 8px rgba(32, 110, 85, 0.10);
    }

    .feature-text {
      flex: 1;
    }

    .feature-title {
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 6px;
      font-size: 1.1rem;
    }

    .feature-desc {
      color: var(--text-secondary);
      font-size: 1.01rem;
    }

    /* Right Calculator */
    .calculator-section {
      background: var(--background-white);
      border-radius: var(--border-radius-lg);
      padding: 30px;
      box-shadow: var(--shadow-md);
      position: sticky;
      top: 100px;
    }

    .calculator-header {
      text-align: center;
      margin-bottom: 25px;
    }

    .calculator-header h2 {
      font-size: 1.5rem;
      color: var(--primary-color);
      margin-bottom: 10px;
      font-weight: 700;
    }

    .calculator-subtitle {
      color: var(--text-secondary);
      font-size: 0.95rem;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: var(--primary-color);
      font-size: 0.95rem;
    }

    .input-row {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .form-input {
      flex: 1;
      padding: 12px 15px;
      border: 2px solid var(--border-color);
      border-radius: var(--border-radius);
      font-size: 1rem;
      transition: var(--transition);
      background: var(--background-white);
    }

    .form-input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(32, 110, 85, 0.1);
    }

    .input-unit {
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 0.95rem;
    }

    .calculate-btn {
      width: 100%;
      padding: 15px;
      background: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--border-radius);
      font-size: 1.1rem;
      font-weight: 700;
      cursor: pointer;
      transition: var(--transition);
      margin-top: 10px;
    }

    .calculate-btn:hover {
      background: var(--primary-dark);
      transform: translateY(-1px);
    }

    .calculate-btn:active {
      transform: translateY(0);
    }

    /* Results Section */
    .results-section {
      margin-top: 60px;
      background: var(--background-white);
      border-radius: var(--border-radius-lg);
      padding: 40px;
      box-shadow: var(--shadow-md);
      display: none;
    }

    .results-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .results-header h2 {
      font-size: 2rem;
      color: var(--primary-color);
      margin-bottom: 10px;
      font-weight: 700;
    }

    .results-subtitle {
      color: #FFFFFF;
      font-size: 2rem;
    }

    /* Legend */
    .legend {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 30px;
      flex-wrap: wrap;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: var(--background-light);
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 500;
    }

    .legend-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

    .legend-dot.pre-period { background-color: #d1a4a0; }
    .legend-dot.period { background-color: #db4171; }
    .legend-dot.post-period { background-color: #bcaa48; }
    .legend-dot.ovulation { background-color: #206e55; }
    .legend-dot.normal { background-color: #e5e7eb; }

    /* Calendar Styles */
    .calendars {
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
      margin-bottom: 30px;
    }

    .calendar {
      background: var(--background-light);
      border-radius: var(--border-radius);
      padding: 20px;
      box-shadow: var(--shadow-sm);
    }

    .calendar h3 {
      text-align: center;
      color: var(--primary-color);
      font-size: 1.2rem;
      margin-bottom: 15px;
      font-weight: 600;
    }

    .calendar-table {
      width: 100%;
      border-collapse: collapse;
      background: var(--background-white);
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--shadow-sm);
    }

    .calendar-table th {
      background: var(--primary-light);
      color: var(--primary-color);
      padding: 12px 8px;
      font-weight: 600;
      font-size: 0.9rem;
      text-align: center;
    }

    .calendar-table td {
      padding: 8px;
      text-align: center;
      vertical-align: middle;
      height: 50px;
    }

    .day {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      margin: 0 auto;
      transition: var(--transition);
      cursor: pointer;
    }

    .day:hover {
      transform: scale(1.1);
    }

    .day.pre-period { background-color: #d1a4a0; }
    .day.period { background-color: #db4171; }
    .day.post-period { background-color: #bcaa48; }
    .day.ovulation { background-color: #206e55; }
    .day.normal { 
      background-color: #e5e7eb; 
      color: var(--text-secondary);
    }

    .result-note {
      text-align: center;
      color: var(--text-muted);
      font-size: 0.9rem;
      font-style: italic;
      padding: 20px;
      background: var(--background-light);
      border-radius: var(--border-radius);
      border-left: 4px solid var(--primary-color);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .content-layout {
        grid-template-columns: 1fr;
        gap: 30px;
      }

      .results-subtitle {
      color: #FFFFFF;
      font-size: 1rem;
    }
      .calculator-section {
        position: static;
      }

      .content-section,
      .calculator-section {
        padding: 25px;
      }

      .content-header h1 {
        font-size: 2rem;
      }

      .legend {
        gap: 10px;
      }

      .calendars {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .main-container {
        padding: 20px 10px;
      }

      .content-section,
      .calculator-section,
      .results-section {
        padding: 20px;
      }

      .navbar {
        padding: 0 10px;
      }

      .nav {
        gap: 15px;
      }

      .talk-to-august {
        padding: 8px 12px;
        font-size: 12px;
      }
    }
    .bmi-style-card {
      background: var(--background-light);
      border-radius: var(--border-radius);
      padding: 48px 40px 40px 40px;
      box-shadow: var(--shadow-lg);
      min-width: 420px;
      max-width: 600px;
      width: 100%;
      margin: 0 auto;
      border: 1px solid #e5e7eb;
    }
    .bmi-card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 18px;
    }
    .bmi-card-logo {
      height: 36px;
      width: auto;
      margin-right: 10px;
    }
    .bmi-card-title-group {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }
    .bmi-card-title {
      font-size: 1.45rem;
      color: var(--primary-color);
      font-weight: 700;
      margin: 0;
      font-family: 'Roboto Condensed', Arial, sans-serif;
    }
    .bmi-card-subtitle {
      font-size: 0.98rem;
      color: var(--text-secondary);
      font-weight: 500;
    }
    .bmi-reset-link {
      background: none;
      border: none;
      color: var(--primary-color);
      font-weight: 600;
      cursor: pointer;
      text-decoration: underline;
      font-size: 0.98rem;
      margin-left: 10px;
      padding: 0;
    }
    .bmi-radio-group {
      display: flex;
      gap: 18px;
      margin-bottom: 18px;
      font-size: 1rem;
      color: var(--text-secondary);
      align-items: center;
    }
    .bmi-calc-btn {
      width: 100%;
      background: var(--primary-color);
      color: #fff;
      font-size: 1.15rem;
      font-weight: 700;
      border-radius: 8px;
      margin-top: 18px;
      padding: 15px 0;
      box-shadow: 0 2px 8px rgba(32, 110, 85, 0.08);
      transition: background 0.2s;
    }
    .bmi-calc-btn:hover {
      background: var(--primary-dark);
    }
    .bmi-calendar-center {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      gap: 32px;
      flex-wrap: wrap;
      margin-bottom: 24px;
    }
    .bmi-results-card {
      background: var(--background-white);
      border-radius: 16px;
      box-shadow: 0 4px 24px rgba(32, 110, 85, 0.10);
      padding: 40px 32px 32px 32px;
      max-width: 1362px;
      margin: 40px auto 0 auto;
      display: none;
    }
    @media (max-width: 1200px) {
      .main-container {
        max-width: 98vw;
        padding: 30px 10px;
      }
      .content-layout {
        grid-template-columns: 1fr 1fr;
        gap: 40px;
      }
      .calculator-section.bmi-style-card {
        min-width: 320px;
        max-width: 100%;
        padding: 32px 18px 28px 18px;
      }
    }
    @media (max-width: 900px) {
      .content-layout {
        grid-template-columns: 1fr;
        gap: 32px;
      }
      .main-container {
        padding: 18px 2vw;
      }
      .calculator-section.bmi-style-card {
        min-width: 0;
        max-width: 100%;
        padding: 18px 8px 18px 8px;
      }
    }
    @media (max-width: 600px) {
      .main-container {
        padding: 6px 2vw;
      }
      /* .calculator-section.bmi-style-card {
        padding: 10px 2px 10px 2px;
      } */
    }

      header {
        background-color: transparent;
        padding: 8px 0px;
        border-bottom: none;
        position: sticky;
        top: 0;
        z-index: 1000;
        transition: all 0.3s ease;
        box-shadow: none;
      }
      .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        margin: 0 auto;
      }
      .logo {
        display: flex;
        align-items: center;
      }
      .logo img {
        height: 60px;
        width: auto;
      }
      .nav {
        display: flex;
        align-items: center;
        gap: 32px;
      }
      .language-switcher {
        display: flex;
        align-items: center;
        gap: 8px;
        /* background: var(--background-white); */
        /* border: 1px solid #e5e7eb;
      border-radius: 6px; */
        padding: 8px 12px;
        font-size: 14px;
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.2s ease;
      }
      .language-switcher:hover {
        /* background: var(--primary-light); */
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
      .language-switcher img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
      .talk-to-august {
        background-color: #206e55;
        color: white !important;
        padding: 10px 18px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        font-size: 16px;
        transition: background-color 0.2s ease;
      }
      .talk-to-august:hover {
        background-color: #1a5a47;
        color: white !important;
      }
      :root {
  --primary-color: #206e55;
  --primary-dark: #1a5a47;
  --primary-light: #f0f9f4;
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-muted: #6b7280;
  --border-color: #e5e7eb;
  --background-light: #f9fafb;
  --background-white: #ffffff;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --transition: all 0.2s ease;
}

* {
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
  color: var(--text-primary);
  line-height: 1.7;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
#container {
  max-width: 1200px;
  margin: 0 auto;
}
header {
  background-color: transparent;
  padding: 8px 0px;
  border-bottom: none;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: none;
}

header.scrolled {
  background-color: #f8f9fa;
  padding: 4px 0px;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  /* max-width: 1430px; */
  margin: 0 auto;
}
.logo {
  display: flex;
  align-items: center;
}
.logo img {
  height: 60px;
  width: auto;
}
.nav {
  display: flex;
  align-items: center;
  gap: 32px;
}
.nav-links {
  display: flex;
  align-items: center;
  gap: 24px;
  list-style: none;
  margin: 0;
  padding: 0;
}
.nav-links a {
  color: #111827;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: color 0.2s ease;
}
.nav-links a:hover {
  color: #206e55;
}
.nav-links a.active {
  color: #206e55;
}
.talk-to-august {
  background-color: #206e55;
  color: white !important;
  padding: 10px 18px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: background-color 0.2s ease;
}
.talk-to-august:hover {
  background-color: #1a5a47;
  color: white !important;
}
main {
  /* padding: 40px 20px; */
  /* max-width: 800px; */
  /* margin: 0 auto; */
  /* background-color: #ffffff; */
  /* border-radius: 8px; */
  /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
  margin-top: 20px;
  margin-bottom: 20px;
}
h1 {
  color: #206e55;
  font-size: 36px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 16px;
}
p {
  color: #1f2937;
  font-size: 18px;
  margin-bottom: 20px;
}
main > p {
  color: #1f2937;
  font-size: 18px;
  margin-bottom: 20px;
  text-align: center;
}
.print-link {
  text-align: right;
  margin-bottom: 10px;
}
.print-link a {
  color: #206e55;
  text-decoration: none;
  font-weight: 500;
}
.print-link a:hover {
  color: #1a5a47;
}
.form-container {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 30px;
  margin-top: 20px;
  max-width: 920px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 2px 10px rgba(0,0,0,0.04);
}
.tabs {
  margin-bottom: 10px;
}
.tab-button {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  padding: 10px 18px;
  cursor: pointer;
  border-radius: 6px 6px 0 0;
  font-weight: 500;
  font-size: 16px;
  color: #6b7280;
  transition: all 0.2s ease;
}
.tab-button:hover {
  background-color: #e5e7eb;
  color: #111827;
}
.tab-button.active {
  background-color: white;
  border-bottom: 2px solid #206e55;
  color: #206e55;
}
.form-instruction {
  color: #1f2937;
  font-size: 18px;
  margin-bottom: 16px;
}
.form-field {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}
.form-field label {
  display: inline-block;
  width: 100px;
  color: #111827;
  font-weight: 600;
  font-size: 16px;
}
.form-field input[type="text"] {
  width: 100px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 10px 14px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}
.form-field input[type="text"]:focus {
  outline: none;
  border-color: #416955;
  box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
}
.form-field select {
  width: 400px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 10px 14px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}
.form-field select:focus {
  outline: none;
  border-color: #416955;
  box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
}
.form-field input[type="radio"] {
  margin-right: 6px;
  accent-color: #416955;
}
.form-field span {
  color: #1f2937;
  font-size: 16px;
}
.settings-link {
  margin-bottom: 20px;
}
.settings-link a {
  color: #206e55;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
}
.settings-link a:hover {
  color: #1a5a47;
}
.form-buttons {
  text-align: left;
  margin-top: 30px;
}
.calculate-button {
  background-color: #206e55;
  color: white;
  padding: 14px 28px;
  border: none;
  border-radius: 6px;
  margin-right: 12px;
  cursor: pointer;
  font-weight: 500;
  font-size: 18px;
  transition: background-color 0.2s ease;
}
.calculate-button:hover {
  background-color: #1a5a47;
}
.clear-button {
  background-color: #f3f4f6;
  color: #111827;
  padding: 14px 28px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 18px;
  transition: all 0.2s ease;
}
.clear-button:hover {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}
.result {
  margin-top: 30px;
  padding: 20px;
  background-color: #f0f9f4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  color: #416955;
  font-size: 20px;
  font-weight: 600;
}
.activity-definitions {
  margin-top: 30px;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}
.activity-definitions h3 {
  color: #111827;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}
.activity-definitions ul {
  list-style-type: disc;
  padding-left: 20px;
  color: #1f2937;
  font-size: 16px;
  line-height: 1.7;
}
.activity-definitions li {
  margin-bottom: 8px;
}

/* Converter Section Styles */
.converter-section {
  margin-top: 40px;
  padding: 30px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}
.converter-section h2 {
  color: #416955;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 16px;
}
.converter-container {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;
}
.converter-input,
.converter-output {
  display: flex;
  align-items: center;
  gap: 12px;
}
.converter-input input {
  width: 100px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 10px 14px;
  font-size: 16px;
}
.converter-input select,
.converter-output select {
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 10px 14px;
  font-size: 16px;
  min-width: 200px;
}
.converter-equals {
  font-size: 20px;
  font-weight: 600;
  color: #416955;
}
#converted-value {
  font-size: 20px;
  font-weight: 600;
  color: #416955;
  min-width: 80px;
}

/* Related Section Styles */
.related-section {
  margin-top: 40px;
  padding: 30px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}
.related-section h2 {
  color: #416955;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 20px;
}
.related-links {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}
.related-link {
  background-color: #206e55;
  color: white;
  padding: 14px 22px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: background-color 0.2s ease;
}
.related-link:hover {
  background-color: #1a5a47;
}

/* Information Section Styles */
.info-section {
  margin-top: 40px;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}
.info-section h2 {
  color: #206e55;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 16px;
}
.info-section h3 {
  color: #111827;
  font-size: 22px;
  font-weight: 600;
  margin: 24px 0 16px 0;
}
.equations-container {
  margin-top: 24px;
}
.equation-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}
.equation-card h4 {
  color: #416955;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}
.equation-card p {
  margin-bottom: 8px;
  font-family: "Courier New", monospace;
  background-color: #f3f4f6;
  padding: 10px;
  border-radius: 4px;
  font-size: 16px;
}
.equation-note {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
  background-color: transparent !important;
  padding: 0 !important;
  font-style: italic;
  color: #374151;
  font-size: 16px;
}
.weight-guidance {
  background-color: #f0f9f4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}
.weight-guidance h4 {
  color: #416955;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}
.weight-guidance ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-bottom: 16px;
}
.weight-guidance li {
  margin-bottom: 8px;
  color: #111827;
}
.warning-note {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 12px;
  color: #92400e;
  font-weight: 500;
  margin-top: 16px;
}

/* Additional Info Sections */
.info-text {
  margin-top: 20px;
}
.info-text p {
  background-color: transparent !important;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
  padding: 0 !important;
  margin-bottom: 16px;
  line-height: 1.6;
}

/* Counting Section Styles */
.counting-section {
  margin-top: 40px;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}
.counting-section h2 {
  color: #416955;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 16px;
}
.steps-container {
  display: grid;
  gap: 20px;
  margin-top: 24px;
}
.step-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #416955;
}
.step-card h4 {
  color: #416955;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}

/* Zigzag Section Styles */
.zigzag-section {
  margin-top: 40px;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}
.zigzag-section h2 {
  color: #416955;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 16px;
}
.zigzag-explanation {
  margin-top: 24px;
}
.zigzag-explanation h3 {
  color: #111827;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 16px;
}
.example-card,
.benefits-card {
  background-color: #f0f9f4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}
.example-card h4,
.benefits-card h4 {
  color: #416955;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}

/* Requirements Section Styles */
.requirements-section {
  margin-top: 40px;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}
.requirements-section h2 {
  color: #416955;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 16px;
}
.factors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 24px;
}
.factor-card,
.guidelines-card,
.minimum-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}
.factor-card h4,
.guidelines-card h4,
.minimum-card h4 {
  color: #206e55;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}
.note {
  font-style: italic;
  color: #374151;
  font-size: 16px;
}
.warning {
  color: #dc2626;
  font-weight: 500;
}

/* Settings Section Styles */
.settings-container {
  display: none;
  margin-top: 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px 8px 16px;
  max-width: 380px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.settings-header {
  background-color: #206e55;
  color: white;
  padding: 8px 14px;
  border-radius: 8px 8px 0 0;
  margin: -16px -16px 10px -16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.settings-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
}
.collapse-button {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}
.settings-section {
  margin-bottom: 12px;
  padding-bottom: 0;
}
.settings-section:last-child {
  margin-bottom: 0;
}
.settings-section h3 {
  color: #111827;
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 6px;
  margin-top: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.radio-option {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 0;
  background: none;
  border: none;
  border-radius: 0;
}
.radio-option label {
  font-size: 13px;
  color: #374151;
  font-weight: 500;
  margin: 0;
}
.body-fat-input {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 0;
  margin-left: 0;
}
.body-fat-input input {
  width: 60px;
  padding: 6px 8px;
  font-size: 13px;
}
.body-fat-input span {
  font-size: 13px;
  color: #6b7280;
}
.info-icon {
  width: 16px;
  height: 16px;
  font-size: 10px;
  margin-left: 4px;
}
@media (max-width: 768px) {
  .form-container {
    max-width: 100%;
    margin: 15px 8px;
  }
  .settings-container {
    max-width: 100%;
    margin: 12px 4px;
    padding: 8px 8px 4px 8px;
  }
  .settings-header {
    margin: -8px -8px 8px -8px;
    padding: 8px 10px;
  }
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
  #container {
    max-width: 100%;
    margin: 0;
    padding: 0 12px;
  }

  main {
    margin: 15px 0;
    padding: 0;
  }

  .navbar {
    padding: 8px 12px;
    /* flex-wrap: wrap; */
  }

  .navbar .logo img {
    height: 45px;
  }

  .navbar .nav-links {
    display: none;
  }

  .talk-to-august {
    padding: 8px 14px;
    font-size: 14px;
  }

  h1 {
    font-size: 24px;
    line-height: 1.3;
    margin-bottom: 12px;
    padding: 0 8px;
    text-align: center;
    color: #206e55;
  }

  main > p {
    font-size: 15px;
    line-height: 1.5;
    margin-bottom: 18px;
    padding: 0 8px;
    text-align: center;
    color: #1f2937;
  }

  .form-container {
    padding: 20px 16px;
    margin: 15px 8px;
    border-radius: 12px;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .form-instruction {
    font-size: 15px;
    margin-bottom: 18px;
    text-align: center;
    color: #374151;
    line-height: 1.4;
  }

  .tabs {
    display: flex;
    justify-content: center;
    gap: 2px;
    margin-bottom: 20px;
    background-color: #f3f4f6;
    border-radius: 8px;
    padding: 4px;
  }

  .tab-button {
    flex: 1;
    max-width: none;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: 6px;
    background-color: transparent;
    color: #6b7280;
    transition: all 0.2s ease;
  }

  .tab-button.active {
    background-color: #416955;
    color: white;
    border: none;
  }

  .tab-button:hover:not(.active) {
    background-color: #e5e7eb;
    color: #374151;
  }

  .form-field {
    margin-bottom: 18px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    min-height: auto;
    background-color: #f9fafb;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  .form-field label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    min-width: auto;
    flex-shrink: 0;
    margin-bottom: 4px;
  }

  .form-field input[type="text"],
  .form-field input[type="number"] {
    width: 100%;
    max-width: none;
    padding: 12px 14px;
    font-size: 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .form-field select {
    width: 100%;
    padding: 12px 14px;
    font-size: 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .form-field span {
    font-size: 13px;
    color: #6b7280;
    margin-left: 0;
    font-style: italic;
  }

  /* Gender radio buttons - horizontal layout */
  .form-field:has(input[type="radio"]) {
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 16px;
    padding: 12px 16px;
  }

  .form-field input[type="radio"] {
    margin-right: 6px;
    margin-left: 0;
    accent-color: #416955;
    width: 18px;
    height: 18px;
  }

  /* Height and weight input containers */
  #height-us,
  #height-metric,
  #weight-us,
  #weight-metric {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
  }

  #height-us input,
  #height-metric input,
  #weight-us input,
  #weight-metric input {
    flex: 1;
    min-width: 80px;
    max-width: 120px;
  }

  .form-buttons {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    justify-content: center;
  }

  .calculate-button,
  .clear-button {
    flex: 1;
    max-width: none;
    padding: 14px 20px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    min-height: 48px;
  }

  .settings-link {
    text-align: center;
    margin: 20px 0;
  }

  .settings-link a {
    display: inline-block;
    padding: 12px 20px;
    background-color: #f8f9fa;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    color: #416955;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.2s ease;
  }

  .settings-link a:hover {
    background-color: #416955;
    color: white;
    border-color: #416955;
  }

  .settings-container {
    margin-top: 20px;
    padding: 16px;
    border-radius: 12px;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .settings-header {
    margin: -16px -16px 20px -16px;
    padding: 16px 20px;
    border-radius: 12px 12px 0 0;
    background-color: #416955;
  }

  .settings-header h3 {
    font-size: 16px;
    color: white;
    margin: 0;
  }

  .collapse-button {
    color: white;
    font-size: 18px;
  }

  .settings-section {
    margin-bottom: 20px;
    background-color: #f9fafb;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  .settings-section h3 {
    font-size: 14px;
    margin-bottom: 12px;
    color: #374151;
    font-weight: 600;
  }

  .radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .radio-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 12px;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .radio-option:hover {
    background-color: #f8f9fa;
    border-color: #416955;
  }

  .radio-option input[type="radio"] {
    margin: 0;
    accent-color: #416955;
  }

  .radio-option label {
    margin: 0;
    font-size: 14px;
    cursor: pointer;
    color: #374151;
    font-weight: 500;
  }

  .results-container {
    margin-top: 20px;
    padding: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .results-header {
    padding: 16px 20px;
  }

  .results-header h2 {
    font-size: 20px;
    margin: 0;
  }

  .download-btn {
    padding: 10px 16px;
    font-size: 12px;
    gap: 6px;
  }

  .download-btn span:last-child {
    display: none;
  }

  .download-icon {
    font-size: 18px;
  }

  .results-content {
    padding: 16px;
  }

  .results-description {
    font-size: 14px;
    margin-bottom: 16px;
    line-height: 1.5;
  }

  .bmr-info-section {
    padding: 15px !important;
    margin-bottom: 20px !important;
  }

  .bmr-info-section h3 {
    font-size: 16px !important;
    margin-bottom: 12px !important;
  }

  .bmr-info-section h4 {
    font-size: 14px !important;
    margin-bottom: 8px !important;
  }

  .bmr-info-section > div > div {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }

  .bmr-info-section > div > div > div {
    padding: 8px !important;
    font-size: 12px !important;
  }

  /* Mobile-first responsive table design */
  .results-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  /* Hide table headers on mobile */
  .results-table thead {
    display: none;
  }

  .results-table tbody {
    display: block;
  }

  .results-table tr {
    display: block;
    margin-bottom: 16px;
    background-color: #f9fafb;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .results-table tr:hover {
    background-color: #f3f4f6;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .results-table td {
    display: block;
    text-align: left;
    padding: 8px 0;
    border: none;
    position: relative;
  }

  /* Add labels before each data cell */
  .results-table td:before {
    content: attr(data-label);
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    display: block;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .results-table td:first-child:before {
    content: "Goal";
  }

  .results-table td:nth-child(2):before {
    content: "Daily Calories";
  }

  .results-table td:nth-child(3):before {
    content: "Body Fat Change";
  }

  .goal-label {
    font-size: 16px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
    display: block;
  }

  .goal-description {
    font-size: 13px;
    color: #6b7280;
    font-style: italic;
    line-height: 1.4;
    margin-bottom: 8px;
  }

  .calorie-value {
    font-size: 24px;
    font-weight: 800;
    color: #416955;
    margin: 8px 0 4px 0;
    display: block;
  }

  .percentage {
    font-size: 14px;
    color: #6b7280;
    font-weight: 600;
    display: block;
  }

  .unit-label {
    font-size: 11px;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 2px;
    display: block;
  }

  /* Special styling for the first column (goal) */
  .results-table td:first-child {
    background-color: #f0f9f4;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    border-left: 4px solid #416955;
  }

  /* Enhanced styling for calorie values */
  .results-table td:nth-child(2) {
    text-align: center;
    background-color: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    border: 2px solid #e5e7eb;
  }

  /* Enhanced styling for body fat values */
  .results-table td:nth-child(3) {
    text-align: center;
    background-color: #fef7ff;
    border-radius: 8px;
    padding: 12px;
    border: 2px solid #e879f9;
  }

  .weight-gain-section {
    margin-top: 15px;
  }

  .activity-definitions {
    margin-top: 20px;
    padding: 15px;
  }

  .activity-definitions h3 {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .activity-definitions ul {
    padding-left: 15px;
  }

  .activity-definitions li {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .info-section {
    margin-top: 30px;
    padding: 15px;
  }

  .info-section h2 {
    font-size: 22px;
    margin-bottom: 15px;
  }

  .info-section h3 {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .info-section p {
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 15px;
  }

  .equations-container {
    margin-top: 20px;
  }

  .equation-card {
    margin-bottom: 20px;
    padding: 15px;
  }

  .equation-card h4 {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .equation-card p {
    font-size: 13px;
    margin-bottom: 8px;
  }

  .equation-note {
    font-size: 12px !important;
  }

  .step-card {
    margin-bottom: 15px;
    padding: 15px;
  }

  .step-card h4 {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .step-card p {
    font-size: 14px;
    line-height: 1.5;
  }

  .warning-note {
    padding: 15px;
    margin: 15px 0;
  }

  .warning-note strong {
    font-size: 14px;
  }

  .warning-note ul {
    padding-left: 15px;
  }

  .warning-note li {
    font-size: 13px;
    margin-bottom: 5px;
  }

  .nutrition-table-container {
    margin: 20px 10px !important;
    padding: 0 10px !important;
    /* max-width: calc(100vw - 20px) !important; */
  }

  .nutrition-table-container h3 {
    font-size: 18px !important;
    margin-bottom: 15px !important;
    text-align: center !important;
  }

  .nutrition-table-container > div {
    margin: 0 !important;
    border-radius: 6px !important;
  }

  .nutrition-table-container table {
    font-size: 11px !important;
    min-width: 500px !important;
  }

  .nutrition-table-container th,
  .nutrition-table-container td {
    padding: 8px 6px !important;
    font-size: 11px !important;
    word-wrap: break-word;
    max-width: 120px;
  }

  .nutrition-table-container th {
    font-size: 12px !important;
    font-weight: 600 !important;
  }

  /* Hide kJ column on small screens to save space */
  .nutrition-table-container table th:last-child,
  .nutrition-table-container table td:last-child {
    display: none;
  }

  /* Improve readability of category headers */
  .nutrition-table-container td[colspan="4"] {
    font-size: 12px !important;
    padding: 10px 8px !important;
    text-align: center !important;
  }
}

@media (max-width: 480px) {
  #container {
    margin: 0;
    padding: 0 8px;
  }

  main {
    padding: 0;
  }

  h1 {
    font-size: 22px;
    padding: 0 4px;
    margin-bottom: 10px;
  }

  main > p {
    font-size: 14px;
    line-height: 1.4;
    padding: 0 4px;
    margin-bottom: 16px;
    text-align: center;
  }

  .form-container {
    padding: 16px 12px;
    margin: 12px 4px;
    border-radius: 10px;
  }

  .form-instruction {
    font-size: 14px;
    margin-bottom: 16px;
  }

  .tabs {
    padding: 3px;
    margin-bottom: 16px;
  }

  .tab-button {
    padding: 10px 12px;
    font-size: 13px;
  }

  .form-field {
    margin-bottom: 16px;
    padding: 10px;
    gap: 6px;
  }

  .form-field label {
    font-size: 13px;
  }

  .form-field input[type="text"],
  .form-field input[type="number"] {
    padding: 10px 12px;
    font-size: 15px;
  }

  .form-field select {
    padding: 10px 12px;
    font-size: 14px;
  }

  .form-field span {
    font-size: 12px;
  }

  .form-buttons {
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
  }

  .calculate-button,
  .clear-button {
    max-width: none;
    padding: 12px 20px;
    font-size: 15px;
    min-height: 44px;
  }

  .settings-container {
    padding: 12px;
    margin-top: 16px;
  }

  .settings-header {
    margin: -12px -12px 16px -12px;
    padding: 12px 16px;
  }

  .settings-section {
    margin-bottom: 16px;
    padding: 10px;
  }

  .settings-section h3 {
    font-size: 13px;
    margin-bottom: 10px;
  }

  .radio-option {
    padding: 8px 10px;
  }

  .radio-option label {
    font-size: 13px;
  }

  /* Enhanced mobile table for very small screens */
  .results-table tr {
    margin-bottom: 12px;
    padding: 12px;
  }

  .results-table td:before {
    font-size: 12px;
    margin-bottom: 3px;
  }

  .goal-label {
    font-size: 15px;
  }

  .goal-description {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .calorie-value {
    font-size: 22px;
    margin: 6px 0 3px 0;
  }

  .percentage {
    font-size: 13px;
  }

  .unit-label {
    font-size: 10px;
  }

  .results-table td:first-child,
  .results-table td:nth-child(2),
  .results-table td:nth-child(3) {
    padding: 10px;
    margin-bottom: 10px;
  }

  .bmr-info-section {
    padding: 10px !important;
  }

  .bmr-info-section h3 {
    font-size: 14px !important;
  }

  .bmr-info-section > div > div > div {
    padding: 6px !important;
    font-size: 11px !important;
  }

  .nutrition-table-container {
    margin: 15px 5px !important;
    padding: 0 5px !important;
  }

  .nutrition-table-container h3 {
    font-size: 16px !important;
    margin-bottom: 12px !important;
  }

  .nutrition-table-container table {
    font-size: 10px !important;
    min-width: 400px !important;
  }

  .nutrition-table-container th,
  .nutrition-table-container td {
    padding: 6px 4px !important;
    font-size: 10px !important;
    max-width: 100px;
  }

  .nutrition-table-container th {
    font-size: 11px !important;
  }

  /* Better spacing for category headers on very small screens */
  .nutrition-table-container td[colspan="4"] {
    font-size: 11px !important;
    padding: 8px 6px !important;
  }

  /* Ensure tables don't break layout on very small screens */
  .nutrition-table-container > div {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }

  /* Hide kJ column and adjust remaining columns */
  .nutrition-table-container table th:last-child,
  .nutrition-table-container table td:last-child {
    display: none;
  }

  .nutrition-table-container table th:first-child,
  .nutrition-table-container table td:first-child {
    min-width: 120px;
  }

  .nutrition-table-container table th:nth-child(2),
  .nutrition-table-container table td:nth-child(2) {
    min-width: 100px;
  }

  .nutrition-table-container table th:nth-child(3),
  .nutrition-table-container table td:nth-child(3) {
    min-width: 80px;
    text-align: center !important;
  }
}

/* Tablet specific styles */
@media (min-width: 769px) and (max-width: 1024px) {
  #container {
    max-width: 95%;
    padding: 20px;
  }

  .form-container {
    padding: 30px 25px;
    margin: 25px auto;
    max-width: 600px;
  }

  .form-field {
    margin-bottom: 22px;
    min-height: 55px;
  }

  .form-field label {
    min-width: 100px;
    font-size: 16px;
  }

  .form-field input[type="text"],
  .form-field input[type="number"] {
    max-width: 140px;
    padding: 11px 14px;
  }

  .form-field select {
    padding: 11px 14px;
    font-size: 16px;
  }

  .form-buttons {
    gap: 15px;
  }

  .calculate-button,
  .clear-button {
    max-width: 180px;
    padding: 15px 24px;
    font-size: 17px;
  }

  .nutrition-table-container {
    margin: 30px auto !important;
    padding: 0 20px !important;
    max-width: 900px !important;
  }

  .nutrition-table-container table {
    font-size: 13px;
  }

  .nutrition-table-container th,
  .nutrition-table-container td {
    padding: 10px 8px !important;
    font-size: 12px !important;
  }

  .nutrition-table-container th {
    font-size: 13px !important;
  }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }

  .form-grid {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .nutrition-table-container {
    /* margin: 40px auto !important; */
    /* max-width: 1100px !important; */
    /* padding: 0 30px !important; */
  }

  .nutrition-table-container table {
    font-size: 15px;
  }

  .nutrition-table-container th,
  .nutrition-table-container td {
    padding: 12px 16px !important;
    font-size: 14px !important;
  }

  .nutrition-table-container th {
    font-size: 16px !important;
  }

  .nutrition-table-container h3 {
    font-size: 24px !important;
    margin-bottom: 25px !important;
  }
}

/* Responsive improvements for height/weight inputs */
@media (max-width: 768px) {
  #height-us,
  #height-metric,
  #weight-us,
  #weight-metric {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
  }

  #height-us input,
  #height-metric input,
  #weight-us input,
  #weight-metric input {
    flex: 1;
    min-width: 80px;
    max-width: 120px;
  }

  #height-us span,
  #height-metric span,
  #weight-us span,
  #weight-metric span {
    margin: 0;
    white-space: nowrap;
  }

  .body-fat-input {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
  }

  .body-fat-input input {
    flex: 1;
    max-width: 100px;
  }

  .body-fat-input span {
    margin: 0;
  }
}

/* Enhanced table responsiveness */
@media (max-width: 768px) {
  .results-table-container {
    overflow: visible;
    padding: 0;
  }

  /* Ensure mobile table layout is maintained */
  .results-table {
    min-width: auto;
    width: 100%;
  }

  /* Keep nutrition table scrollable */
  .nutrition-table-container > div {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: 0 -15px;
    /* padding: 0 15px; */
  }

  .nutrition-table-container table {
    min-width: 500px;
  }

  /* Add smooth scrolling for better UX */
  .results-container {
    scroll-behavior: smooth;
  }

  /* Improve spacing between table rows */
  .results-table tr:last-child {
    margin-bottom: 0;
  }
}

/* Enhanced nutrition table styles for all screen sizes */
.nutrition-table-container {
  position: relative;
  margin: 20px 0;
}

.nutrition-table-container table {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nutrition-table-container th {
  background-color: #416955 !important;
  color: white !important;
  font-weight: 600;
  text-align: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.nutrition-table-container td {
  background-color: white;
  transition: background-color 0.2s ease;
}

.nutrition-table-container tr:hover td {
  background-color: #f8f9fa;
}

.nutrition-table-container tr:nth-child(even) td {
  background-color: #f9fafb;
}

.nutrition-table-container tr:nth-child(even):hover td {
  background-color: #f1f3f4;
}

/* Category header styling */
.nutrition-table-container td[colspan="4"] {
  background-color: #f0f9f4 !important;
  color: #206e55 !important;
  font-weight: 600 !important;
  text-align: center !important;
  border-top: 2px solid #206e55 !important;
  border-bottom: 1px solid #bbf7d0 !important;
}

.nutrition-table-container td[colspan="4"]:hover {
  background-color: #e6f7ea !important;
}

/* Mobile-specific table improvements */
@media (max-width: 768px) {
  .nutrition-table-container {
    margin: 16px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .nutrition-table-container > div {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #416955 #f1f1f1;
  }

  .nutrition-table-container > div::-webkit-scrollbar {
    height: 6px;
  }

  .nutrition-table-container > div::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .nutrition-table-container > div::-webkit-scrollbar-thumb {
    background: #416955;
    border-radius: 3px;
  }

  .nutrition-table-container > div::-webkit-scrollbar-thumb:hover {
    background: #2d4a3a;
  }
}

/* Enhanced mobile form styling */
@media (max-width: 768px) {
  /* Form field focus states */
  .form-field input[type="text"]:focus,
  .form-field input[type="number"]:focus,
  .form-field select:focus {
    outline: none;
    border-color: #206e55;
    box-shadow: 0 0 0 3px rgba(32, 110, 85, 0.1);
  }

  /* Better spacing for form elements */
  .form-field:last-of-type {
    margin-bottom: 25px;
  }

  /* Improved button styling */
  .calculate-button {
    background-color: #206e55;
    color: white;
    border: none;
    font-weight: 500;
    transition: background-color 0.2s ease;
  }

  .calculate-button:hover {
    background-color: #1a5a47;
  }

  .clear-button {
    background-color: #f3f4f6;
    color: #111827;
    border: 1px solid #d1d5db;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .clear-button:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
  }

  /* Settings improvements */
  .settings-section {
    margin-bottom: 20px;
  }

  .settings-section h3 {
    font-size: 16px;
    margin-bottom: 12px;
    color: #111827;
  }

  .body-fat-input {
    margin-left: 0;
    margin-top: 10px;
  }

  .body-fat-input input {
    width: 80px;
    max-width: 80px;
  }
}

/* Enhanced Mobile Touch and Accessibility */
@media (max-width: 768px) {
  .calculate-button,
  .clear-button,
  .collapse-button {
    min-height: 48px; /* Enhanced touch target size */
    touch-action: manipulation;
    cursor: pointer;
  }

  .tab-button {
    min-height: 48px;
    touch-action: manipulation;
    cursor: pointer;
  }

  .settings-link a {
    display: inline-block;
    padding: 12px 16px;
    min-height: 48px;
    line-height: 24px;
    touch-action: manipulation;
  }

  /* Enhanced form field styling for mobile */
  .form-field input[type="text"]:focus,
  .form-field input[type="number"]:focus,
  .form-field select:focus {
    outline: none;
    border-color: #206e55;
    box-shadow: 0 0 0 3px rgba(32, 110, 85, 0.15);
    transform: scale(1.02);
    transition: all 0.2s ease;
  }

  /* Improved radio button styling */
  .form-field input[type="radio"] {
    transform: scale(1.2);
    margin-right: 8px;
  }

  /* Better visual hierarchy */
  .form-field {
    transition: all 0.2s ease;
  }

  .form-field:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
  }

  /* Enhanced button states */
  .calculate-button:active {
    transform: scale(0.98);
    background-color: #1a5a47;
  }

  .clear-button:active {
    transform: scale(0.98);
    background-color: #d1d5db;
  }

  .tab-button:active {
    transform: scale(0.98);
  }

  /* Improved spacing for better touch targets */
  .form-field:has(input[type="radio"]) {
    padding: 16px;
    gap: 20px;
  }

  /* Better visual feedback for active states */
  .tab-button.active {
    box-shadow: 0 2px 4px rgba(32, 110, 85, 0.2);
  }
}

.settings-section {
  margin-bottom: 30px;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section h3 {
  color: #111827;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.radio-option input[type="radio"] {
  margin: 0;
  accent-color: #416955;
  width: 18px;
  height: 18px;
}

.radio-option label {
  color: #111827;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  flex: 1;
}

.body-fat-input {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 8px;
  margin-left: 28px;
}

.body-fat-input input {
  width: 80px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.body-fat-input input:focus {
  outline: none;
  border-color: #416955;
  box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
}

.body-fat-input span {
  color: #6b7280;
  font-size: 14px;
}

.info-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: #e5e7eb;
  color: #6b7280;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  cursor: help;
  margin-left: 8px;
}

.info-icon:hover {
  background-color: #416955;
  color: white;
}

/* Enhanced Results Styles */
.results-container {
  margin-top: 30px;
  display: none;
}

.results-header {
  background-color: #206e55;
  color: white;
  padding: 16px 20px;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.download-btn {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #206e55;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 12px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.download-btn:hover {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  color: #1a5a47;
}

.download-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.download-icon {
  font-size: 16px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

.results-content {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-top: none;
  border-radius: 0 0 8px 8px;
  padding: 20px;
}

.results-description {
  color: #111827;
  font-size: 16px;
  margin-bottom: 20px;
  line-height: 1.6;
}

.results-table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.results-table th,
.results-table td {
  border: 1px solid #e5e7eb;
  padding: 16px 20px;
  text-align: left;
  vertical-align: top;
}

.results-table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #111827;
  font-size: 16px;
  text-align: center;
  border-bottom: 2px solid #416955;
}

.results-table td {
  background-color: #ffffff;
  transition: background-color 0.2s ease;
}

.results-table tr:hover td {
  background-color: #f8f9fa;
}

.results-table tr:nth-child(even) td {
  background-color: #f9fafb;
}

.results-table tr:nth-child(even):hover td {
  background-color: #f1f3f4;
}

.goal-label {
  font-weight: 600;
  color: #111827;
  font-size: 16px;
  margin-bottom: 4px;
}

.goal-description {
  font-size: 14px;
  color: #6b7280;
  margin-top: 2px;
  font-style: italic;
}

.calorie-value {
  font-size: 28px;
  font-weight: 700;
  color: #416955;
  margin-bottom: 4px;
  text-align: center;
}

.percentage {
  font-size: 14px;
  color: #6b7280;
  text-align: center;
  font-weight: 500;
}

.unit-label {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 2px;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Desktop table layout - restore traditional table */
@media (min-width: 769px) {
  .results-table {
    font-size: 16px;
    border-collapse: collapse;
    display: table;
  }

  .results-table thead {
    display: table-header-group;
  }

  .results-table tbody {
    display: table-row-group;
  }

  .results-table tr {
    display: table-row;
    margin-bottom: 0;
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    border: none;
    box-shadow: none;
  }

  .results-table tr:hover {
    background-color: #f8f9fa;
    transform: none;
    box-shadow: none;
  }

  .results-table th,
  .results-table td {
    display: table-cell;
    padding: 20px 24px;
    border: 1px solid #e5e7eb;
    background-color: transparent;
    border-radius: 0;
    margin-bottom: 0;
  }

  .results-table th {
    background-color: #f9fafb !important;
    font-weight: 600;
    color: #111827 !important;
    font-size: 18px;
    text-align: center;
    border-bottom: 2px solid #416955;
  }

  .results-table td {
    background-color: #ffffff;
    transition: background-color 0.2s ease;
  }

  .results-table tr:nth-child(even) td {
    background-color: #f9fafb;
  }

  .results-table tr:nth-child(even):hover td {
    background-color: #f1f3f4;
  }

  /* Remove mobile-specific pseudo-elements */
  .results-table td:before {
    display: none;
  }

  .calorie-value {
    font-size: 32px;
    margin: 0;
    display: inline;
  }

  .goal-label {
    font-size: 18px;
    margin-bottom: 4px;
    display: block;
  }

  .goal-description {
    font-size: 15px;
    margin-bottom: 0;
  }

  .percentage {
    font-size: 15px;
    display: inline;
  }

  .unit-label {
    font-size: 13px;
    margin-top: 2px;
    display: block;
  }

  /* Better column alignment for desktop */
  .results-table th:first-child,
  .results-table td:first-child {
    text-align: left;
    width: 35%;
  }

  .results-table th:nth-child(2),
  .results-table td:nth-child(2),
  .results-table th:nth-child(3),
  .results-table td:nth-child(3) {
    text-align: center;
    width: 32.5%;
  }
}

.weight-gain-toggle {
  margin-top: 20px;
}

.toggle-link {
  color: #206e55;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
}

.toggle-link:hover {
  color: #1a5a47;
  text-decoration: underline;
}

.weight-gain-section {
  display: none;
  margin-top: 20px;
}

/* Types Section Styles */
.types-section {
  margin-top: 40px;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}
.types-section h2 {
  color: #416955;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 16px;
}
.calorie-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 24px;
}
.type-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}
.type-card h4 {
  color: #416955;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}
.thermic-effect {
  margin-top: 30px;
  background-color: #f0f9f4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 20px;
}
.thermic-effect h3 {
  color: #206e55;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
}

/* Additional Mobile Utilities */
@media (max-width: 768px) {
  /* Prevent horizontal scroll */
  html,
  body {
    overflow-x: hidden;
    width: 100%;
  }

  /* Ensure all containers respect mobile width */
  /* * {
    max-width: 100%;
  } */

  /* Better mobile typography */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    word-wrap: break-word;
    hyphens: auto;
  }

  /* Improved mobile form validation styles */
  .form-field input:invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  .form-field input:valid {
    border-color: #10b981;
  }

  /* Enhanced mobile loading states */
  .calculate-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  /* Better mobile error states */
  .error-message {
    color: #ef4444;
    font-size: 14px;
    margin-top: 4px;
    padding: 8px 12px;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
  }

  /* Mobile-optimized animations */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Improved mobile focus management */
  .form-field input:focus,
  .form-field select:focus {
    scroll-margin-top: 100px;
  }

  /* Better mobile table scrolling */
  .results-table-container {
    position: relative;
    overflow: visible;
    scroll-behavior: smooth;
    padding: 0;
  }

  /* Ensure mobile cards don't overflow */
  .results-table tr {
    max-width: 100%;
    word-wrap: break-word;
  }

  /* Mobile-specific print styles */
  @media print {
    .navbar,
    .settings-link,
    .form-buttons {
      display: none !important;
    }

    .form-container {
      box-shadow: none !important;
      border: 1px solid #000 !important;
    }
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

.form-container, .results-container, .info-section, .nutrition-table-container, .calculator-section, .step-card, .requirements-section, .types-section {
  background: var(--background-white) !important;
  color: var(--text-primary) !important;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid #e5e7eb;
}

h1, h2, h3, h4, .results-header h2, .info-card h2, .info-card h3, .calculator-title {
  color: var(--primary-color) !important;
}

.download-btn, .calculate-button, .btn {
  background: var(--primary-color) !important;
  color: #fff !important;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background 0.2s;
}

.download-btn:hover, .calculate-button:hover, .btn:hover {
  background: var(--primary-dark) !important;
}
  
