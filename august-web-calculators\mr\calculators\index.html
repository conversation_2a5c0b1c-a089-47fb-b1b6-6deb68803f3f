<!DOCTYPE html>
<html lang="mr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>दैनिक कॅलरी गरजांचा कॅल्क्युलेटर - अचूक BMR आणि TDEE कॅल्क्युलेटर | MeetAugust</title>
    <meta name="description" content="{{description}}" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/mr/calculators/"
    />
    <link
      rel="icon"
      href="/mr/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link rel="stylesheet" href="/mr/calculators/landing.css" />
    <link
      rel="preload"
      href="/mr/calculators/assets/august_logo_green_nd4fn9.svg"
      as="image"
    />
    <style>
      :root {
        --primary-color: #206e55;
        --primary-dark: #1a5a47;
        --primary-light: #f0f9f4;
        --text-primary: #111827;
        --text-secondary: #374151;
        --background-light: #f9fafb;
        --background-white: #fff;
        --border-radius: 12px;
        --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.08);
      }
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: var(--background-light);
        margin: 0;
        color: var(--text-primary);
        min-height: 100vh;
      }
      header {
        background-color: transparent;
        padding: 8px 0px;
        border-bottom: none;
        position: sticky;
        top: 0;
        z-index: 1000;
        transition: all 0.3s ease;
        box-shadow: none;
      }
      header.scrolled {
    background-color: #f8f9fa;
    padding: 4px 0px;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
      .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        margin: 0 auto;
      }
      .logo {
        display: flex;
        align-items: center;
      }
      .logo img {
        height: 60px;
        width: auto;
      }
      .nav {
        display: flex;
        align-items: center;
        gap: 32px;
      }
      .language-switcher {
        display: flex;
        align-items: center;
        gap: 8px;
        /* background: var(--background-white); */
        padding: 8px 12px;
        font-size: 14px;
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.2s ease;
      }
      .language-switcher:hover {
        background: var(--primary-light);
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
      .language-switcher img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
      .talk-to-august {
        background-color: #206e55;
        color: white !important;
        padding: 10px 18px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        font-size: 16px;
        transition: background-color 0.2s ease;
      }
      .talk-to-august:hover {
        background-color: #1a5a47;
        color: white !important;
      }
      .hero {
        text-align: center;
        margin-top: 40px;
        margin-bottom: 32px;
      }
      .hero h1 {
        color: var(--primary-color);
        font-size: 2.8rem;
        font-weight: 700;
        margin-bottom: 12px;
      }
      .hero p {
        color: var(--text-secondary);
        font-size: 1.25rem;
        margin-bottom: 0;
      }
      .main-content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        margin-top: 32px;
        gap: 16px;
        padding: 0 20px;
      }
      .card {
        background: var(--background-white);
        border-radius: var(--border-radius);
        min-height: 200px;
        box-shadow: var(--shadow-lg);
        padding: 32px 28px 28px 28px;
        max-width: 400px;
        width: 100%;
        text-align: center;
        margin-bottom: 32px;
        border: 1px solid #e5e7eb;
        transition: box-shadow 0.2s;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 220px;
      }
      .card:hover {
        box-shadow: 0 12px 32px rgba(32, 110, 85, 0.12);
      }
      .card-title {
        font-size: 1.5rem;
        color: var(--primary-color);
        font-weight: 600;
        margin-bottom: 10px;
      }
      .card-desc {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 18px;
      }
      .card-action {
        display: flex;
        justify-content: center;
        margin-top: auto;
      }
      .card-action a {
        background: var(--primary-color);
        color: #fff;
        text-decoration: none;
        padding: 12px 32px;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 500;
        transition: background 0.2s;
        box-shadow: 0 2px 8px rgba(32, 110, 85, 0.08);
      }
      .card-action a:hover {
        background: var(--primary-dark);
      }

      header.scrolled {
        background-color: #f8f9fa;
        padding: 4px 0px;
        border-bottom: 1px solid #e5e7eb;
        position: sticky;
        top: 0;
        z-index: 1000;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      .nav-links {
        display: flex;
        align-items: center;
        gap: 24px;
        list-style: none;
        margin: 0;
        padding: 0;
      }
      .nav-links a {
        color: #111827;
        text-decoration: none;
        font-weight: 500;
        font-size: 16px;
        transition: color 0.2s ease;
      }
      .nav-links a:hover {
        color: #206e55;
      }
      .nav-links a.active {
        color: #206e55;
      }
      
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }

      /* MOBILE RESPONSIVE STYLES */
      @media (max-width: 768px) {
        .navbar {
          /* flex-direction: column; */
          padding: 12px 20px;
          gap: 12px;
        }
        
        .logo img {
          height: 50px;
        }
        
        .nav {
          gap: 16px;
        }
        
        .hero {
          margin-top: 20px;
          margin-bottom: 24px;
          padding: 0 20px;
        }
        
        .hero h1 {
          font-size: 2.2rem;
          margin-bottom: 8px;
        }
        
        .hero p {
          font-size: 1.1rem;
        }
        
        .main-content {
          margin-top: 20px;
          padding: 0 16px;
          gap: 20px;
        }
        
        .card {
          max-width: none;
          width: 100%;
          margin-bottom: 20px;
          padding: 24px 20px;
          height: auto;
          min-height: 180px;
        }
        
        .card-title {
          font-size: 1.3rem;
          margin-bottom: 8px;
        }
        
        .card-desc {
          font-size: 1rem;
          margin-bottom: 16px;
        }
        
        .card-action a {
          padding: 10px 24px;
          font-size: 1rem;
        }
        
        .talk-to-august {
          padding: 8px 16px;
          font-size: 14px;
        }
        
        #language-select {
          min-width: 100px;
          font-size: 0.9rem;
          padding: 6px 12px;
        }
        
        #language-select option {
          font-size: 0.9rem;
        }
      }

      /* EXTRA SMALL MOBILE DEVICES */
      @media (max-width: 480px) {
        .navbar {
          padding: 8px 16px;
        }
        
        .hero {
          padding: 0 16px;
        }
        
        .hero h1 {
          font-size: 1.8rem;
          line-height: 1.2;
        }
        
        .hero p {
          font-size: 1rem;
        }
        
        .main-content {
          padding: 0 12px;
          gap: 16px;
        }
        
        .card {
          padding: 20px 16px;
          min-height: 160px;
        }
        
        .card-title {
          font-size: 1.2rem;
          line-height: 1.3;
        }
        
        .card-desc {
          font-size: 0.95rem;
          line-height: 1.4;
        }
        
        .card-action a {
          padding: 8px 20px;
          font-size: 0.95rem;
        }
        
        .nav {
          gap: 12px;
        }
        
        .talk-to-august {
          padding: 6px 12px;
          font-size: 13px;
        }
        
        #language-select {
          min-width: 90px;
          font-size: 0.85rem;
          padding: 4px 8px;
        }
      }

      /* LARGE TABLET DEVICES */
      @media (min-width: 769px) and (max-width: 1024px) {
        .main-content {
          padding: 0 32px;
        }
        
        .card {
          max-width: 350px;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/mr/calculators/">
            <img
              width="200"
              src="/mr/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="कॅल्क्युलेटर लोगो"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=calculator_homepage_topnav"
            class="talk-to-august"
            >ऑगस्टशी बोला</a
          >
        </div>
      </div>
    </header>
    <section class="hero">
      <h1>आरोग्य गणकांमध्ये आपले स्वागत आहे</h1>
      <p>वैज्ञानिक आधारावर आधारित साधनांसह आपल्या आरोग्य प्रवासाला मदत करा.</p>
    </section>
    <main class="main-content">
      <div class="card">
        <div>
          <div class="card-title">कॅलरी कॅल्क्युलेटर</div>
          <div class="card-desc">वजन कमी करण्यासाठी, राखण्यासाठी किंवा स्नायू वाढवण्यासाठी आपली दैनंदिन कॅलरी आवश्यकता शोधा. जलद, अचूक आणि मोफत.</div>
        </div>
        <div class="card-action">
          <a href="/mr/calculators/calorie-calculator/index.html"
            >कॅलरी कॅल्क्युलेटरकडे जा</a
          >
        </div>
      </div>
      <div class="card">
        <div>
          <div class="card-title">BMI कॅल्क्युलेटर</div>
          <div class="card-desc">तुमचा बॉडी मास इंडेक्स (BMI) त्वरित तपासा आणि तुम्ही कोणत्या वजन श्रेणीत येता ते पहा. 20+ वर्षे वयोगटातील प्रौढांसाठी. जलद, अचूक आणि मोफत.</div>
        </div>
        <div class="card-action">
          <a id="bmi-link" href="/mr/calculators/bmi-calculator/index.html"
            >BMI कॅल्क्युलेटरकडे जा</a
          >
        </div>
      </div>
      <div class="card">
        <div>
          <div class="card-title">तुमची प्रसूती तारीख मोजा</div>
          <div class="card-desc">तुमची अपेक्षित प्रसूती तारीख आणि गर्भधारणेचे वय मोजा. मोफत, अचूक आणि भविष्यातील मातांसाठी वापरण्यास सोपे.</div>
        </div>
        <div class="card-action">
          <a
            id="pregnancy-link"
            href="/mr/calculators/pregnancy-calculator/index.html"
            >गर्भधारण कॅल्क्युलेटरकडे जा</a
          >
        </div>
      </div>
      <!-- You can add more cards for other calculators here in the future -->
          <div class="card">
        <div>
          <div class="card-title">मासिक पाळी ट्रॅकर</div>
          <div class="card-desc">तुमच्या मासिक पाळीचा मागोवा ठेवल्याने तुमच्या चक्र आणि एकूण आरोग्याबद्दल अधिक समजण्यास मदत होऊ शकते. यामुळे तुम्हाला नेहमी तयार राहण्यासाठी योजना बनवण्यासही मदत होईल. आमच्या मासिक पाळी ट्रॅकरचा वापर करून तुमच्या मासिक चक्राचा मागोवा कसा ठेवावा याबद्दल अधिक जाणून घ्या.</div>
        </div>
        <div class="card-action">
          <a
            id="pregnancy-link"
            href="/mr/calculators/period-tracker/index.html"
            >मासिक पाळी ट्रॅकरवर जा</a
          >
        </div>
      </div>
      <!-- //// -->
       <div class="card">
        <div>
          <div class="card-title">मासिक पाळी कॅल्क्युलेटर</div>
          <div class="card-desc">आपल्या मासिक पाळीचे गणन करा जेणेकरून आपण आपल्या ओव्हुलेशन आणि प्रजनन खिडक्या अधिक चांगल्या प्रकारे समजू शकाल. आमच्या मासिक पाळी कॅल्क्युलेटरचा उपयोग करून आपली पुढील मासिक पाळी अंदाजा आणि त्यानुसार योजना बनवा.</div>
        </div>
        <div class="card-action">
          <a
            id="pregnancy-link"
            href="/mr/calculators/period-calculator/index.html"
            >मासिक पाळी कॅल्क्युलेटरवर जा</a
          >
        </div>
      </div>
      <!-- ////// -->
       <div class="card">
        <div>
          <div class="card-title">ओव्हुलेशन ट्रॅकर</div>
          <div class="card-desc">आपल्या ओव्हुलेशनचे निरीक्षण करा जेणेकरून आपल्या सर्वात प्रजननक्षम दिवसांचा मागोवा घेता येईल आणि गर्भधारणेची शक्यता वाढेल. आपल्या सायकलचे निरीक्षण करण्यासाठी आणि प्रभावीपणे नियोजन करण्यासाठी आमच्या ओव्हुलेशन ट्रॅकरचा वापर करा.</div>
        </div>
        <div class="card-action">
          <a
            id="pregnancy-link"
            href="/mr/calculators/ovulation-tracker/index.html"
            >ओव्हुलेशन ट्रॅकरवर जा</a
          >
        </div>
      </div>
      <!-- ///// -->
       <div class="card">
        <div>
          <div class="card-title">प्रसूती तारीख कॅल्क्युलेटर</div>
          <div class="card-desc">आपल्या शेवटच्या मासिक पाळीच्या किंवा गर्भधारणेच्या तारखेच्या आधारावर आपल्या बाळाची प्रसूती तारीख अंदाजा. आपल्या गरोदरपणाची योजना बनवण्यासाठी आणि बाळाच्या आगमनासाठी तयारी करण्यासाठी आमच्या प्रसूती तारीख कॅल्क्युलेटरचा वापर करा.</div>
        </div>
        <div class="card-action">
          <a
            id="pregnancy-link"
            href="/mr/calculators/due-date-calculator/index.html"
            >प्रसूती तारीख कॅल्क्युलेटरवर जा</a
          >
        </div>
      </div>
    </main>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
      // Set BMI calculator link to the correct language path
      //   document.getElementById('bmi-link').href = `/${currentLang}/calculators/bmi`;
      //   // Set Pregnancy calculator link to the correct language path
      //   document.getElementById('pregnancy-link').href = `/${currentLang}/calculators/pregnancy`;
      function handleScroll() {
  const header = document.querySelector("header");
  if (header) {
    if (window.scrollY === 0) {
      header.classList.remove("scrolled");
    } else {
      header.classList.add("scrolled");
    }
  }
}
window.addEventListener("scroll", handleScroll);
handleScroll();
    </script>
  </body>
</html>