<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>성인용 BMI 계산기 – 정확한 BMI 도구 | MeetAugust</title>
    <meta
      name="description"
      content="고급 도구로 BMI를 계산하세요. 즉시 결과를 받고 체중 범주를 확인하세요. 무료, 정확, 과학적 근거."
    />
    <meta
      name="keywords"
      content="bmi 계산기, 체질량 지수, 체중 범주, 건강 계산기"
    />
    <meta name="author" content="MeetAugust" />
    <meta
      property="og:title"
      content="성인용 BMI 계산기 – 정확한 BMI 도구"
    />
    <meta
      property="og:description"
      content="BMI를 계산하고 즉시 체중 범주를 확인하세요."
    />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="성인용 BMI 계산기 – 정확한 BMI 도구"
    />
    <meta
      name="twitter:description"
      content="BMI를 계산하고 즉시 체중 범주를 확인하세요."
    />
    <link rel="canonical" href="https://www.meetaugust.ai/ko/calculators/bmi-calculator" />
    <link rel="icon" href="/ko/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/ko/calculators/bmi-calculator/style.css">
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
        <div class="navbar">
          <div class="logo">
            <a href="/ko/calculators/">
                <img
              width="200"
              src="/ko/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="계산기 로고"
            />
            </a>
          </div>
          <div class="nav">
            <div class="language-switcher" id="language-switcher">
              <select id="language-select">
                <!-- Options will be populated by JS -->
              </select>
            </div>
            <a
              href="https://app.meetaugust.ai/join/app?utm=bmi_calculator_topnav"
              class="talk-to-august"
              >August와 대화하기</a
            >
          </div>
        </div>
      </header>
        <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">성인용 BMI 계산기</h1>
          <div class="page-info">
            <div class="info-badge">
              <div class="shield-icon">✓</div>
              <span>모두를 위해</span>
            </div>
          </div>
        </div>

        <div class="content-grid">
          <div class="info-section">
            <h2>성인 BMI 이해: 알아야 할 것들</h2>
            <p class="info-text">
              성인용 BMI 계산기는 키에 비해 체중이 적절한지 평가하는 신뢰할 수 있는 도구입니다. ...
            </p>

            <img
              src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BMI Scale"
              class="weight-image"
            />

            <div style="margin-top: 30px">
              <h3
                style="
                  color: var(--primary-color);
                  font-size: 20px;
                  margin-bottom: 15px;
                "
              >
                성인용 무료 BMI 계산기 (20세 이상)
              </h3>
              <p style="color: var(--text-secondary); margin-bottom: 15px">
                이 성인용 BMI 계산기를 사용하여 즉시 BMI를 확인하고 저체중, 정상 체중, 과체중 또는 비만 등 체중 범주를 확인하세요. 이 계산기는 20세 이상 성인을 위해 설계되었습니다.
              </p>
              <!-- <p style="color: var(--text-secondary)">
                BMI는 전반적인 건강과 체중 관련 위험을 평가하는 데 사용되는 여러 도구 중 하나입니다. 의료 이력, 생활 습관, 신체 검사 결과, 실험실 검사 등 다른 임상 평가와 함께 해석해야 합니다. -->
                <!-- <a href="#" style="color: var(--primary-color)">
                  체질량 지수에 대해 더 알아보기
                </a>
                그리고 이것이 귀하의 건강 프로필에 어떻게 적용되는지 확인하세요. 항상 개인 맞춤형 조언을 위해 의료 전문가와 상담하세요 — 이 도구는 교육 목적으로만 제공되며 의료 조언을 대체하지 않습니다.
              </p> -->
            </div>
          </div>

          <div class="calculator-section">
            <div style="display: flex; justify-content: space-between;" >

              <h3 class="calculator-title">BMI 계산기</h3>
              <button class="download-btn  neon-pulse" style="margin-left: 2rem; margin-top: 0; display: none; align-self: flex-start; font-size: 1.15rem; padding: 1rem 2rem;" id="download-pdf-btn" onclick="downloadBMIResultsPDF()"><span style="font-size:1.5em;vertical-align:middle;">📄</span> Download BMI Results</button>
            </div>

            <div class="unit-selector">
              <div class="unit-option">
                <input
                  type="radio"
                  id="us-units"
                  name="units"
                  value="us"
                  checked
                />
                <label for="us-units">미국 단위</label>
              </div>
              <div class="unit-option">
                <input
                  type="radio"
                  id="metric-units"
                  name="units"
                  value="metric"
                />
                <label for="metric-units">미터법 단위</label>
              </div>
              <span class="reset-link" onclick="resetCalculator()">초기화</span>
            </div>

            <div class="form-group">
              <label class="form-label">키</label>
              <div class="input-group" id="height-inputs">
                <input
                  type="number"
                  class="form-input"
                  id="height-feet"
                  placeholder="0"
                  min="0"
                  max="10"
                />
                <span class="unit-label">피트 (ft)</span>
                <input
                  type="number"
                  class="form-input"
                  id="height-inches"
                  placeholder="0"
                  min="0"
                  max="11"
                />
                <span class="unit-label">인치 (in)</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">체중</label>
              <div class="input-group">
                <input
                  type="number"
                  class="form-input"
                  id="weight"
                  placeholder="0"
                  min="0"
                />
                <span class="unit-label" id="weight-unit">파운드 (lbs)</span>
              </div>
            </div>

            <button class="calculate-btn" onclick="calculateBMI()">
              계산하기
            </button>
          </div>
        </div>

        <div class="note-section">
          <p>
            <strong>참고:</strong> 이 BMI 계산기는 자바스크립트가 필요합니다. 브라우저에서 자바스크립트가 비활성화되어 있거나 문제가 발생하면 다음 공식을 사용해 수동으로 BMI를 계산할 수 있습니다:
            <em>BMI = 체중(kg) / (키(m) × 키(m))</em>
            예: 체중이 70kg이고 키가 1.75m인 경우, BMI는 22.9입니다.
          </p>
        </div>
      </main>

<!-- Result section moved below main content -->
<div class="result-section-wrapper">
  <div class="result-section" id="result-section">
    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
      <div>
        <div class="bmi-value" id="bmi-value">0.0</div>
        <div class="bmi-category" id="bmi-category"></div>
        <div class="bmi-description" id="bmi-description"></div>
      </div>
    </div>
  </div>
</div>

    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="/ko/calculators/bmi-calculator/script.js"></script>
    <script>
      const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
      const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
      const select = document.getElementById('language-select');
      const currentLang = window.location.pathname.split('/')[1] || 'en';
      locales.forEach(l => {
        const opt = document.createElement('option');
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener('change', function() {
        const newLang = this.value;
        let path = window.location.pathname.split('/');
        if (locales.includes(path[1])) { path[1] = newLang; }
        else { path = ['', newLang, ...path.slice(1)]; }
        localStorage.setItem('preferredLang', newLang);
        window.location.pathname = path.join('/');
      });
      // Persist language choice
      if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
        select.value = localStorage.getItem('preferredLang');
      }
    </script>
  </body>
</html> 