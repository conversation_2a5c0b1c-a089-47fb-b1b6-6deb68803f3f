<!DOCTYPE html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Täglicher Kalorienbedarf <PERSON> - Präziser BMR & TDEE Rechner | MeetAugust</title>
    <meta name="description" content="Berechnen Sie Ihren täglichen Kalorienbedarf mit unserem fortschrittlichen BMR-Rechner. Erhalten Sie personalisierte Kalorienziele für Gewichtsverlust, Erhaltung und Muskelaufbau. Kostenlos, präzise und wissenschaftlich fundiert." />
    <meta name="keywords" content="<PERSON><PERSON><PERSON><PERSON><PERSON>, B<PERSON>, TDEE <PERSON>, täglicher Kalorienbedarf, Gewichtsverlust Rechner, Stoffwechsel Rechner, Ernährungsplanung" />
    <meta name="author" content="MeetAugust" />
    <meta property="og:title" content="Täglicher Kalorienbedarf <PERSON>ner - Präziser BMR & TDEE Rechner" />
    <meta property="og:description" content="Berechnen Sie Ihren täglichen Kalorienbedarf mit unserem fortschrittlichen BMR-Rechner. Erhalten Sie personalisierte Kalorienziele für Gewichtsverlust, Erhaltung und Muskelaufbau." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Täglicher Kalorienbedarf Rechner - Präziser BMR & TDEE Rechner" />
    <meta name="twitter:description" content="Berechnen Sie Ihren täglichen Kalorienbedarf mit unserem fortschrittlichen BMR-Rechner. Erhalten Sie personalisierte Kalorienziele für Gewichtsverlust, Erhaltung und Muskelaufbau." />
    <link rel="canonical" href="https://www.meetaugust.ai/calculator/calorie" />
    
    <link rel="canonical" href="https://www.meetaugust.ai/de/calculators/calorie-calculator" />
    <link rel="icon" href="/de/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/de/calculators/calorie-calculator/style.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/de/calculators/">
            <img
          width="200"
          src="/de/calculators/assets/august_logo_green_nd4fn9.svg"
          alt="Rechner Logo"
        />
        </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=calorie_cal_topnav"
            class="talk-to-august"
            >Sprechen Sie mit August</a
          >
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <h1>Fortschrittlicher Täglicher Kalorienbedarf Rechner</h1>
        <p>Finden Sie heraus, wie viele Kalorien Sie täglich benötigen mit unserem präzisen Kalorienrechner. Perfekt für Gewichtsverlust, Muskelaufbau, <br /> oder Aufrechterhaltung eines gesunden Lebensstils basierend auf Ihrem Körper, Zielen und Aktivitätsniveau.</p>
        <section class="form-container">
          <div class="tabs">
            <button class="tab-button" data-unit="us">US Einheiten</button>
            <button class="tab-button active" data-unit="metric">Metrische Einheiten</button>
          </div>
          <p class="form-instruction">Geben Sie Ihre persönlichen Daten unten ein und klicken Sie auf Berechnen, um Ihre maßgeschneiderten Kalorienempfehlungen zu erhalten</p>
          <form id="calorie-form">
            <div class="form-field">
              <label for="age">Alter</label>
              <input type="text" id="age" value="25" />
              <span>Alter 15 - 80</span>
            </div>
            <div class="form-field">
              <label>Geschlecht</label>
              <input type="radio" name="gender" value="male" checked /> Männlich
              <input type="radio" name="gender" value="female" /> Weiblich
            </div>
            <div class="form-field">
              <label for="height">Größe</label>
              <!-- US Units (feet and inches) -->
              <div id="height-us" style="display: none">
                <input
                  type="text"
                  id="height-feet"
                  value="5"
                  style="width: 60px"
                />
                <span>Fuß</span>
                <input
                  type="text"
                  id="height-inches"
                  value="10"
                  style="width: 60px"
                />
                <span>Zoll</span>
              </div>
              <!-- Metric Units (cm) -->
              <div id="height-metric" style="display: none">
                <input type="text" id="height-cm" value="180" />
                <span>cm</span>
              </div>
            </div>
            <div class="form-field">
              <label for="weight">Gewicht</label>
              <!-- US Units (pounds) -->
              <div id="weight-us" style="display: none">
                <input type="text" id="weight-lbs" value="165" />
                <span>Pfund</span>
              </div>
              <!-- Metric Units (kg) -->
              <div id="weight-metric" style="display: none">
                <input type="text" id="weight-kg" value="65" />
                <span>kg</span>
              </div>
            </div>
            <div class="form-field">
              <label for="activity">Aktivität</label>
              <select id="activity">
                <option value="sedentary">Sitzend: wenig oder keine Bewegung</option>
                <option value="light">Leicht aktiv: leichte Bewegung 1-3 Tage/Woche</option>
                <option value="moderate" selected>Mäßig aktiv: mäßige Bewegung 3-5 Tage/Woche</option>
                <option value="very">Sehr aktiv: intensive Bewegung 6-7 Tage/Woche</option>
                <option value="super">Super aktiv: sehr intensive Bewegung, körperliche Arbeit</option>
              </select>
            </div>
            <div class="settings-link">
              <a href="#" id="settings-link">+ Einstellungen</a>
            </div>

            <!-- Settings Section (inline) -->
            <div id="settings-container" class="settings-container">
              <div class="settings-header">
                <h3>Einstellungen</h3>
                <button class="collapse-button" id="collapse-settings">
                  −
                </button>
              </div>

              <div class="settings-section">
                <h3>Ergebnis-Einheit:</h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="calories-unit"
                      name="results-unit"
                      value="calories"
                      checked
                    />
                    <label for="calories-unit">Kalorien</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="kilojoules-unit"
                      name="results-unit"
                      value="kilojoules"
                    />
                    <label for="kilojoules-unit">Kilojoule</label>
                  </div>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Körperfettanteil:
                  <span
                    class="info-icon"
                    title="Geben Sie Ihren Körperfettanteil für genauere Körperzusammensetzungsberechnungen ein"
                    >?</span
                  >
                </h3>
                <div
                  class="body-fat-input"
                  style="display: flex; margin-left: 0"
                >
                  <input
                    type="number"
                    id="user-body-fat"
                    min="5"
                    max="50"
                    step="0.1"
                    value="20"
                  />
                  <span>%</span>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  BMR-Schätzformel:
                  <span
                    class="info-icon"
                    title="Wählen Sie die Formel zur Berechnung Ihres Grundumsatzes"
                    >?</span
                  >
                </h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="mifflin-formula"
                      name="bmr-formula"
                      value="mifflin"
                      checked
                    />
                    <label for="mifflin-formula">Mifflin St Jeor</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="harris-formula"
                      name="bmr-formula"
                      value="harris"
                    />
                    <label for="harris-formula">Überarbeitete Harris-Benedict</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="katch-formula"
                      name="bmr-formula"
                      value="katch"
                    />
                    <label for="katch-formula">Katch-McArdle</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-buttons">
              <button type="button" class="calculate-button">
                Berechnen ▶
              </button>
              <button type="button" class="clear-button">Löschen</button>
            </div>
          </form>
        </section>

        <!-- Enhanced Results Section -->
        <div id="results-container" class="results-container">
          <div class="results-header">
            <h2>Ergebnis</h2>
            <button
              class="download-btn"
              onclick="downloadResultsPDF()"
              title="Ergebnisse als PDF herunterladen"
            >
              <span class="download-icon">📥</span>
              <span>PDF herunterladen</span>
            </button>
          </div>
          <div class="results-content">
            <p class="results-description">Ihre personalisierten Kalorienziele werden mit fortschrittlichen Stoffwechselformeln berechnet. Diese Empfehlungen bieten tägliche Kalorienaufnahme-Richtlinien, die auf Ihre spezifischen Ziele zugeschnitten sind - ob Sie Ihr aktuelles Gewicht halten, nachhaltigen Gewichtsverlust erreichen oder gesunde Gewichtszunahme unterstützen möchten.</p>

            <!-- BMR and Activity Information - always hidden -->
            <div
              class="bmr-info-section"
              style="
                margin-bottom: 30px;
                display:none;
                padding: 20px;
                background-color: #f9fafb;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
              "
            >
              <h3
                style="
                  color: #416955;
                  font-size: 18px;
                  font-weight: 600;
                  margin-bottom: 16px;
                "
              >
                Grundumsatz (BMR):
                <span id="bmr-value" style="color: #111827">1,650</span>
                Kalorien/Tag
              </h3>

              <div style="margin-bottom: 16px">
                <h4
                  style="
                    color: #111827;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                  "
                >
                  Aktivitätsniveaus:
                </h4>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    font-size: 14px;
                  "
                >
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Sitzend:</strong> wenig oder keine Bewegung
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Leicht:</strong> Bewegung 1-3 mal/Woche
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Mäßig:</strong> Bewegung 4-5 mal/Woche
                  </div>
                  <div
                    id="active-highlight"
                    style="
                      padding: 8px;
                      background-color: #e0f2e7;
                      border-radius: 4px;
                      border: 2px solid #416955;
                    "
                  >
                    <strong>Aktiv:</strong> tägliche Bewegung oder intensive Bewegung 3-4 mal/Woche
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Sehr Aktiv:</strong> intensive Bewegung 6-7 mal/Woche
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Extra Aktiv:</strong> sehr intensive Bewegung täglich, oder körperliche Arbeit
                  </div>
                </div>
              </div>
            </div>

            <table class="results-table" id="results-table">
              <thead>
                <tr>
                  <th style="width: 40%">Ziel</th>
                  <th style="width: 30%">Kalorien</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-label="Ziel">
                    <div class="goal-label">Gewicht halten</div>
                  </td>
                  <td data-label="Tägliche Kalorien">
                    <div class="calorie-value" id="maintain-calories">
                      2,549
                    </div>
                    <div class="percentage">100%</div>
                    <div class="unit-label">Kalorien/Tag</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Ziel">
                    <div class="goal-label">Leichter Gewichtsverlust</div>
                    <div class="goal-description">0.5 lb/Woche</div>
                  </td>
                  <td data-label="Tägliche Kalorien">
                    <div class="calorie-value" id="mild-loss-calories">
                      2,299
                    </div>
                    <div class="percentage">90%</div>
                    <div class="unit-label">Kalorien/Tag</div>
                  </td>
                </tr>
                <tr>
                  <td data-label="Ziel">
                    <div class="goal-label">Gewichtsverlust</div>
                    <div class="goal-description">1 lb/Woche</div>
                  </td>
                  <td data-label="Tägliche Kalorien">
                    <div class="calorie-value" id="weight-loss-calories">
                      2,049
                    </div>
                    <div class="percentage">80%</div>
                    <div class="unit-label">Kalorien/Tag</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Ziel">
                    <div class="goal-label">Extremer Gewichtsverlust</div>
                    <div class="goal-description">2 lb/Woche</div>
                  </td>
                  <td data-label="Tägliche Kalorien">
                    <div class="calorie-value" id="extreme-loss-calories">
                      1,549
                    </div>
                    <div class="percentage">61%</div>
                    <div class="unit-label">Kalorien/Tag</div>
                  </td>
                </tr>
              </tbody>
            </table>

            <div
              class="weight-gain-toggle"
              style="text-align: left; margin-top: 20px; padding-left: 0"
            >
              <a
                href="javascript:void(0)"
                class="toggle-link"
                id="weight-gain-toggle"
                >Informationen für Gewichtszunahme anzeigen</a
              >
            </div>

            <div class="weight-gain-section" id="weight-gain-section">
              <h3 style="color: #416955; margin-bottom: 16px">
                Gewichtszunahme Information
              </h3>
              <table class="results-table">
                <thead>
                  <tr>
                    <th style="width: 40%">Ziel</th>
                    <th style="width: 30%">Kalorien</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Ziel">
                      <div class="goal-label">Leichte Gewichtszunahme</div>
                      <div class="goal-description">0.25 kg/Woche</div>
                    </td>
                    <td data-label="Tägliche Kalorien">
                      <div class="calorie-value" id="mild-gain-calories">
                        2,799
                      </div>
                      <div class="percentage">112%</div>
                      <div class="unit-label">Kalorien/Tag</div>
                    </td>
                  </tr>
                  <tr>
                    <td data-label="Ziel">
                      <div class="goal-label">Gewichtszunahme</div>
                      <div class="goal-description">0.5 kg/Woche</div>
                    </td>
                    <td data-label="Tägliche Kalorien">
                      <div class="calorie-value" id="weight-gain-calories">
                        3,049
                      </div>
                      <div class="percentage">124%</div>
                      <div class="unit-label">Kalorien/Tag</div>
                    </td>
                  </tr>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Ziel">
                      <div class="goal-label">Schnelle Gewichtszunahme</div>
                      <div class="goal-description">1 kg/Woche</div>
                    </td>
                    <td data-label="Tägliche Kalorien">
                      <div class="calorie-value" id="fast-gain-calories">
                        3,549
                      </div>
                      <div class="percentage">148%</div>
                      <div class="unit-label">Kalorien/Tag</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div id="result" style="display: none" class="result"></div>
        <div class="activity-definitions">
          <h2>Richtlinien für körperliche Aktivität</h2>
          <ul>
            <li>
              <strong>Leichte Bewegung:</strong> 20-40 Minuten mäßig intensiver Aktivitäten wie Gehen oder sanftes Yoga.
            </li>
            <li>
              <strong>Mäßige Bewegung:</strong> 30-60 Minuten Aktivitäten, die Ihre Herzfrequenz erhöhen, wie zügiges Gehen oder Radfahren.
            </li>
            <li>
              <strong>Intensive Bewegung:</strong> 45-90 Minuten hochintensives Training, Sport oder anspruchsvolle körperliche Aktivitäten.
            </li>
            <li>
              <strong>Professionelles/Athletisches Training:</strong> 2+ Stunden intensives Training oder körperlich anspruchsvolle berufliche Arbeit.
            </li>
          </ul>
        </div>

        <!-- Nutritional Reference Tables Section -->
        <section class="info-section">
          <h2>Vollständiger Ernährungsreferenzleitfaden</h2>
          <p>Verwenden Sie diese umfassenden Tabellen, um fundierte Ernährungsentscheidungen zu treffen und den Kaloriengehalt alltäglicher Lebensmittel, Strategien zur Mahlzeitenplanung und Energieverbrauch durch Bewegung besser zu verstehen.</p>

          <!-- Food Calorie Table -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Kaloriengehalt beliebter Lebensmittel
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 600px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Lebensmittel
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Portionsgröße
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Kalorien
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      kJ
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Frische Früchte
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mango
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 mittelgroße (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      135
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      565
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Kiwi
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 große (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      56
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      234
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Heidelbeeren
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      84
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      351
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Avocado
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 mittelgroße (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      160
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      670
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Kirschen
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      97
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      406
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Frisches Gemüse
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Süßkartoffel
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 mittelgroße (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      112
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      469
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Paprika
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse geschnitten
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      28
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      117
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Spinat
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2 Tassen frisch
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      14
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      59
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Zucchini
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse geschnitten
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      19
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      80
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Blumenkohl
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      25
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      105
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Proteinquellen
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Lachs, gegrillt
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      175
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      732
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Putenbrust
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Griechischer Joghurt
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      6 oz. Behälter
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      130
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      544
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mandeln
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 oz. (23 Nüsse)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      164
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      686
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Linsen, gekocht
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 Tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      115
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      481
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Getreide & Stärke
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Quinoa, gekocht
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      222
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      929
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Haferflocken
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse gekocht
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      154
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      644
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Vollkornnudeln
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse gekocht
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      174
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      728
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Brauner Reis
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse gekocht
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      218
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      912
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Getränke
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Grüner Tee
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      8
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mandelmilch
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      39
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      163
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Kokoswasser
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 Tasse
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      46
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      192
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Rotwein
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      5 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p style="font-size: 12px; color: #6b7280; font-style: italic">
              Hinweis: Kalorienwerte sind ungefähr und können je nach Zubereitungsmethoden und spezifischen Marken variieren.
            </p>
          </div>

          <!-- Sample Meal Plans -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Strategische Mahlzeitenplanung
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 700px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Mahlzeitenzeit
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      1,300 Kalorien Plan
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      1,600 Kalorien Plan
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      2,100 Kalorien Plan
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Frühstück
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 Tasse griechischer Joghurt mit 1/2 Tasse Heidelbeeren (130 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 Tasse Haferflocken mit 1/2 Tasse Heidelbeeren (238 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 Tasse Haferflocken mit 1 Kiwi, 1 oz. Mandeln (458 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Morgensnack
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 kleine Kiwi (56 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 mittelgroße Mango (135 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 mittelgroße Mango, 10 Kirschen (232 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Gesamt Vormittag
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      186 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      373 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      690 cal
                    </td>
                  </tr>

                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Mittagessen
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      2 Tassen Spinatsalat mit 3 oz. gegrilltem Lachs (189 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. Putenbrust, 1 Tasse Zucchini, 1/2 Tasse Quinoa (264 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. gegrillter Lachs, 1 Tasse brauner Reis, 1 Tasse Blumenkohl (418 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Nachmittagssnack
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 Tasse Paprikastreifen (28 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 Avocado (160 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 Avocado, 1 oz. Mandeln (324 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Gesamt Mittag
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      217 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      424 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      742 cal
                    </td>
                  </tr>

                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Abendessen
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. Putenbrust, 1 Tasse Blumenkohl (150 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. gegrillter Lachs, 1 Tasse Süßkartoffel (287 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. Putenbrust, 1 Tasse Vollkornnudeln, 1 Tasse Spinat (313 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Abendsnack
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 Tasse grüner Tee (2 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 Tasse Kokoswasser (46 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 Tasse griechischer Joghurt (130 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Gesamt Abend
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      152 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      333 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      443 cal
                    </td>
                  </tr>
                  <tr style="background-color: #416955; color: white">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Tagesgesamt
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1,255 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1,630 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      2,175 cal
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Comprehensive Information Section -->
        <section class="info-section">
          <h2>Meistern Sie Ihren Stoffwechsel</h2>
          <p>Das Verständnis Ihres Stoffwechsels ist der Schlüssel zum Erreichen Ihrer Gesundheitsziele. Dieser Rechner verwendet wissenschaftlich validierte Formeln, um Ihren Grundumsatz (BMR) und Gesamtenergieverbrauch (TDEE) zu schätzen.</p>

          <div class="equations-container">
            <h3>Drei Bewährte Formeln für BMR</h3>
            <p>Dieser Rechner verwendet drei gut erforschte Gleichungen zur Schätzung Ihres BMR, jede mit einzigartigen Stärken je nach Ihrem Profil:</p>

            <div class="equation-card">
              <h4>Mifflin-St Jeor Gleichung</h4>
              <p>
                <strong>Männlich:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) + 5
              </p>
              <p>
                <strong>Weiblich:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) - 161
              </p>
              <p class="equation-note">
                Weithin als die genaueste für die Allgemeinbevölkerung angesehen, besonders für Nicht-Sportler.
              </p>
            </div>

            <div class="equation-card">
              <h4>Überarbeitete Harris-Benedict Gleichung</h4>
              <p>
                <strong>Männlich:</strong> BMR = 13.397 × weight + 4.799
                × height - 5.677 × age + 88.362
              </p>
              <p>
                <strong>Weiblich:</strong> BMR = 9.247 × weight +
                3.098 × height - 4.330 × age + 447.593
              </p>
              <p class="equation-note">
                Eine vertrauenswürdige Formel, die für moderne Genauigkeit aktualisiert wurde, geeignet für eine breite Palette von Personen.
              </p>
            </div>

            <div class="equation-card">
              <h4>Katch-McArdle Formel</h4>
              <p>BMR = 370 + 21.6 × (1 − body fat percentage) × weight (kg)</p>
              <p class="equation-note">
                Ideal für Personen mit bekannten Körperfettanteilen, da sie die fettfreie Körpermasse berücksichtigt.
              </p>
            </div>

            <div class="info-text">
              <h3>TDEE: BMR in umsetzbare Ziele umwandeln</h3>
              <p>Ihr Gesamtenergieverbrauch (TDEE) ist Ihr BMR multipliziert mit einem Aktivitätsfaktor, der Ihren Lebensstil widerspiegelt. Dies gibt Ihnen ein vollständiges Bild Ihres täglichen Kalorienbedarfs.</p>

              <div
                class="activity-table"
                style="
                  margin: 20px 0;
                  background-color: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  padding: 20px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #416955; color: white">
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Aktivitätsniveau
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Beschreibung
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Multiplikator
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Sitzend
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        wenig oder keine Bewegung
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.2
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Leicht Aktiv
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Bewegung 1-3 mal/Woche
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.375
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Mäßig Aktiv
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Bewegung 4-5 mal/Woche
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.55
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Sehr Aktiv
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        intensive Bewegung 6-7 mal/Woche
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.725
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Super Aktiv
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        sehr intensive Bewegung täglich, oder körperliche Arbeit
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.9
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p>Das Ergebnis ist eine präzise Schätzung der Kalorien, die Sie täglich verbrennen, die Sie verwenden können, um Ihre Ernährung für Gewichtsverlust, Erhaltung oder Zunahme anzupassen.</p>

              <h3>Strategisches Gewichtsmanagement</h3>
              <p>Um Gewicht zu verlieren, benötigen Sie ein Kaloriendefizit; um zuzunehmen, einen Überschuss. Dieser Rechner bietet präzise Kalorienziele für jedes Ziel und gewährleistet nachhaltigen Fortschritt.</p>

              <div class="warning-note">
                <strong>Kritische Richtlinien für sicheren Gewichtsverlust:</strong>
                <ul style="margin: 8px 0; padding-left: 20px">
                  <li>Vermeiden Sie extreme Defizite, um eine Verlangsamung Ihres Stoffwechsels zu verhindern.</li>
                  <li>Stellen Sie ausreichend Protein sicher, um Muskelmasse zu erhalten.</li>
                  <li>Priorisieren Sie nährstoffreiche Lebensmittel, um Mängel zu vermeiden.</li>
                  <li>Vermeiden Sie Crash-Diäten, um Gewichtszunahme zu verhindern.</li>
                </ul>
                <p style="margin-top: 12px">Für optimale Ergebnisse konsultieren Sie einen Ernährungsberater oder Diätassistenten, um Ihren Plan weiter zu personalisieren.</p>
              </div>

              <h3>Ernährungsoptimierungsstrategien</h3>
              <ul
                style="
                  list-style-type: disc;
                  padding-left: 20px;
                  margin: 16px 0;
                "
              >
                <li>Priorisieren Sie nährstoffreiche Lebensmittel wie Gemüse, mageres Protein und Vollkornprodukte.</li>
                <li>Stellen Sie ausreichende Proteinaufnahme sicher, um Muskelaufbau und Sättigung zu unterstützen.</li>
                <li>Lehnen Sie übermäßig restriktive Diäten zugunsten nachhaltiger, ausgewogener Ernährung ab.</li>
                <li>Verfolgen Sie Ihre Aufnahme konsistent, um langfristige gesunde Gewohnheiten aufzubauen.</li>
              </ul>

              <p style="font-weight: 500; color: #416955; margin-top: 20px">
                Dieser Rechner ist ein Ausgangspunkt. Passen Sie basierend auf Ihrem Fortschritt an und konsultieren Sie Fachleute für personalisierte Beratung.
              </p>
            </div>
          </div>
        </section>

        <!-- Calorie Counting Section -->
        <section class="counting-section">
          <h2>Präzises Kalorienverfolgung</h2>

          <div class="steps-container">
            <div class="step-card">
              <h4>Schritt 1: Bestimmen Sie Ihre Stoffwechselbasis</h4>
              <p>Verwenden Sie diesen Rechner, um Ihren BMR und TDEE basierend auf Ihrem Alter, Geschlecht, Gewicht, Größe und Aktivitätsniveau zu finden.</p>
            </div>

            <div class="step-card">
              <h4>Schritt 2: Stellen Sie Ihre Gewichtsziele auf</h4>
              <p>Setzen Sie realistische Ziele für Gewichtsverlust, Erhaltung oder Zunahme, verwenden Sie die bereitgestellten Kalorienempfehlungen.</p>
            </div>

            <div class="step-card">
              <h4>Schritt 3: Implementieren Sie Überwachungssysteme</h4>
              <p>Verfolgen Sie Ihre Kalorienaufnahme mit Apps oder einem Ernährungstagebuch, um mit Ihren Zielen in Einklang zu bleiben.</p>
              <p>Wiegen Sie sich wöchentlich und überwachen Sie Trends, nicht tägliche Schwankungen.</p>
            </div>

            <div class="step-card">
              <h4>Schritt 4: Optimieren Sie durch Bewertung</h4>
              <p>Bewerten Sie Ihren Kalorienbedarf alle 4-6 Wochen oder nach signifikanten Gewichtsveränderungen neu, um Ihren Plan effektiv zu halten.</p>
            </div>
          </div>

          <div class="info-text">
            <h3>Die Wissenschaft des Kaloriengleichgewichts</h3>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li><strong>Grundlagen des Energiegleichgewichts:</strong> Gewichtsmanagement wird durch Kalorienaufnahme versus Kalorienverbrauch bestimmt.</li>
              <li><strong>Thermischer Effekt der Nahrung:</strong> Verschiedene Lebensmittel benötigen unterschiedliche Energiemengen zur Verdauung, was den Gesamtkalorienverbrauch beeinflusst.</li>
              <li><strong>Sättigung und Lebensmittelqualität:</strong> Ballaststoff- und proteinreiche Lebensmittel fördern die Sättigung und helfen Ihnen, bei Ihrem Plan zu bleiben.</li>
            </ul>

            <p style="font-style: italic; color: #374151; margin: 16px 0">
              Beispiel: Die 'Twinkie-Diät' zeigte, dass Gewichtsverlust mit einem Kaloriendefizit möglich ist, aber nährstoffarme Diäten schaden der langfristigen Gesundheit.
            </p>

            <h4 style="color: #416955; margin-top: 20px">Zusätzliche Vorteile der Kalorienverfolgung</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Baut Ernährungsbewusstsein und achtsame Essgewohnheiten auf.</li>
              <li>Verbessert die Portionskontrolle durch konsistente Verfolgung.</li>
              <li>Verbindet Lebensmittelauswahl mit Bewegung, um das Energiegleichgewicht zu optimieren.</li>
            </ul>
          </div>
        </section>

        <!-- Zigzag Calorie Cycling Section -->
        <section class="zigzag-section">
          <h2>Zickzack-Kalorienzyklus</h2>
          <p>Zickzack-Kalorienzyklus beinhaltet die Variation Ihrer täglichen Kalorienaufnahme bei Beibehaltung Ihres wöchentlichen Ziels, um die Stoffwechselflexibilität zu verbessern und Plateaus zu verhindern.</p>

          <div class="zigzag-explanation">
            <div class="example-card">
              <h4>Beispiel</h4>
              <p><strong>Wöchentliches Ziel:</strong> 14,000 Kalorien (2,000 Kalorien/Tag Durchschnitt)</p>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li><strong>Option A:</strong> 7 Tage bei 2,000 Kalorien.</li>
                <li><strong>Option B:</strong> 5 Tage bei 1,800 Kalorien, 2 Tage bei 2,500 Kalorien.</li>
              </ul>
              <p>Beide Optionen erreichen das wöchentliche Ziel, variieren aber die tägliche Aufnahme, um Ihren Stoffwechsel dynamisch zu halten.</p>
            </div>

            <div class="benefits-card">
              <h4>Zickzack-Ziel</h4>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li>Bessere Stoffwechselflexibilität und Einhaltung.</li>
                <li>Mehr Flexibilität bei der Mahlzeitenplanung, besonders für soziale Veranstaltungen.</li>
                <li>Verhindert Stoffwechselanpassung durch längere Defizite.</li>
                <li>Durchbricht Gewichtsverlust-Plateaus durch Variation der Kalorienaufnahme.</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Calorie Requirements Section -->
        <section class="requirements-section">
          <h2>Kalorienbedarf nach Lebensstil</h2>
          <p>Kalorienbedarf variiert nach individuellen Faktoren, aber allgemeine Richtlinien können Ihnen beim Start helfen.</p>

          <div class="factors-grid">
            <div class="factor-card">
              <h4>Faktoren, die den Kalorienbedarf beeinflussen</h4>
              <ul>
                <li>Alter, Geschlecht, Gewicht und Größe.</li>
                <li>Aktivitätsniveau (sitzend bis hochaktiv).</li>
                <li>Gesundheitszustand, einschließlich Schwangerschaft oder medizinischer Bedingungen.</li>
              </ul>
            </div>

            <div class="guidelines-card">
              <h4>Allgemeine Richtlinien</h4>
              <p><strong>Männer:</strong> 2,000–3,000 Kalorien/Tag</p>
              <p><strong>Frauen:</strong> 1,600–2,400 Kalorien/Tag</p>
            </div>

            <div class="minimum-card">
              <h4>Minimale sichere Aufnahme</h4>
              <p><strong>Frauen:</strong> 1,200 Kalorien/Tag</p>
              <p><strong>Männer:</strong> 1,500 Kalorien/Tag</p>
              <p class="warning">Aufnahmen unter diesen Niveaus sollten medizinisch überwacht werden.</p>
            </div>
          </div>

          <div class="warning-note" style="margin-top: 20px">
            <p>Übermäßige Kalorienrestriktion kann zu Nährstoffmängeln, Muskelverlust und Stoffwechselverlangsamung führen. Priorisieren Sie immer die Gesundheit.</p>
          </div>
        </section>

        <!-- Calorie Types Section -->
        <section class="types-section">
          <h2>Nicht alle Kalorien sind gleich</h2>
          <p>Kalorien stammen aus verschiedenen Makronährstoffen, jeder mit einzigartigen Auswirkungen auf Ihren Körper:</p>
          <ul style="list-style-type: disc; padding-left: 20px; margin: 16px 0">
            <li>Protein: 4 Kalorien/Gramm – unterstützt Muskelreparatur und Sättigung.</li>
            <li>Kohlenhydrate: 4 Kalorien/Gramm – primäre Energiequelle.</li>
            <li>Fett: 9 Kalorien/Gramm – essentiell für Hormone und Nährstoffaufnahme.</li>
            <li>Alkohol: 7 Kalorien/Gramm – minimaler Nährwert.</li>
          </ul>

          <p>Nährwertkennzeichnungen liefern genaue Kalorienangaben, aber Portionsgrößen und Zubereitungsmethoden sind wichtig.</p>

          <div class="calorie-types">
            <div class="type-card">
              <h4>Hochkalorische Lebensmittel</h4>
              <p>Reich an Kalorien, oft aufgrund von Fetten oder Zuckern. Sparsam verwenden für Gewichtsmanagement.</p>
              <ul>
                <li>Avocados, Öle.</li>
                <li>Nüsse und Samen.</li>
                <li>Frittierte Lebensmittel.</li>
                <li>Zuckerhaltige Desserts und Snacks.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Niedrigkalorische Lebensmittel</h4>
              <ul>
                <li>Viele Gemüsesorten (z.B. Spinat, Zucchini).</li>
                <li>Einige Früchte (z.B. Beeren).</li>
                <li>Mageres Protein (z.B. Pute, Fisch).</li>
                <li>Vollkornprodukte in Maßen.</li>
                <li>Blattgemüse für Volumen und Nährstoffe.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Leere Kalorien</h4>
              <ul>
                <li>Zuckerhaltige Getränke (z.B. Limonade).</li>
                <li>Verarbeitete Snacks (z.B. Chips, Kekse).</li>
                <li>Zugesetzte Zucker in verpackten Lebensmitteln.</li>
                <li>Feste Fette (z.B. Butter, Margarine).</li>
                <li>Alkohol mit minimalem Nährwert.</li>
              </ul>
            </div>
          </div>

          <div class="thermic-effect">
            <h3>Warum Kalorienqualität wichtig ist</h3>
            <p>Getränke wie Limonade oder Alkohol fügen Kalorien ohne Sättigung hinzu, was es schwieriger macht, ein Defizit aufrechtzuerhalten.</p>

            <h4 style="color: #416955; margin-top: 16px">Erstellen Sie einen ausgewogenen Plan</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Konzentrieren Sie sich auf vollwertige, unverarbeitete Lebensmittel für Nährstoffdichte.</li>
              <li>Begrenzen Sie zuckerhaltige Snacks und Getränke.</li>
              <li>Verwenden Sie ballaststoff- und proteinreiche Lebensmittel für natürliche Portionskontrolle.</li>
              <li>Kombinieren Sie Kalorienzählen mit Bewegung für nachhaltige Ergebnisse.</li>
            </ul>
          </div>
        </section>

        <!-- Final Takeaway Section -->
        <section class="info-section" style="margin-top: 40px">
          <h2>Letzte Erkenntnis</h2>
          <p>Es gibt keinen einheitlichen Ansatz für Ernährung. Verwenden Sie diesen Rechner als Ausgangspunkt, verfolgen Sie Ihren Fortschritt und passen Sie nach Bedarf an, um Ihre einzigartigen Bedürfnisse zu erfüllen.</p>
          <p style="font-weight: 500; color: #416955">Verfolgen Sie weise, essen Sie achtsam und priorisieren Sie langfristige Gesundheit.</p>
        </section>
      </main>
    </div>
    <script src="/de/calculators/calorie-calculator/index.js"></script>
    <script>
    const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
    const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
    const select = document.getElementById('language-select');
    const currentLang = window.location.pathname.split('/')[1] || 'en';
    locales.forEach(l => {
      const opt = document.createElement('option');
      opt.value = l;
      opt.textContent = languageNames[l] || l;
      if (l === currentLang) opt.selected = true;
      select.appendChild(opt);
    });
    select.addEventListener('change', function() {
      const newLang = this.value;
      let path = window.location.pathname.split('/');
      if (locales.includes(path[1])) { path[1] = newLang; }
      else { path = ['', newLang, ...path.slice(1)]; }
      localStorage.setItem('preferredLang', newLang);
      window.location.pathname = path.join('/');
    });
    // Persist language choice
    if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
      select.value = localStorage.getItem('preferredLang');
      select.dispatchEvent(new Event('change'));
    }
    </script>
  </body>
</html>