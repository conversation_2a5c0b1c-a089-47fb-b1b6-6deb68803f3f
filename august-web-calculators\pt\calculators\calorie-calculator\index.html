<!DOCTYPE html>
<html lang="pt">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Calculadora de Necessidades Calóricas Diárias - Calculadora Precisa de BMR & TDEE | MeetAugust</title>
    <meta name="description" content="Calcule suas necessidades calóricas diárias com nossa avançada calculadora de BMR. Obtenha metas calóricas personalizadas para perda de peso, manutenção e ganho muscular. Grátis, precisa e baseada na ciência." />
    <meta name="keywords" content="calculadora de calorias, calculadora BMR, calculadora TDEE, necessidades calóricas diárias, calculadora de perda de peso, calculadora de metabolismo, planejamento nutricional" />
    <meta name="author" content="MeetAugust" />
    <meta property="og:title" content="Calculadora de Necessidades Calóricas Diárias - Calculadora Precisa de BMR & TDEE" />
    <meta property="og:description" content="Calcule suas necessidades calóricas diárias com nossa avançada calculadora de BMR. Obtenha metas calóricas personalizadas para perda de peso, manutenção e ganho muscular." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Calculadora de Necessidades Calóricas Diárias - Calculadora Precisa de BMR & TDEE" />
    <meta name="twitter:description" content="Calcule suas necessidades calóricas diárias com nossa avançada calculadora de BMR. Obtenha metas calóricas personalizadas para perda de peso, manutenção e ganho muscular." />
    <link rel="canonical" href="https://www.meetaugust.ai/calculator/calorie" />
    
    <link rel="canonical" href="https://www.meetaugust.ai/pt/calculators/calorie-calculator" />
    <link rel="icon" href="/pt/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/pt/calculators/calorie-calculator/style.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/pt/calculators/">
            <img
          width="200"
          src="/pt/calculators/assets/august_logo_green_nd4fn9.svg"
          alt="Logo da Calculadora"
        />
        </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=calorie_cal_topnav"
            class="talk-to-august"
            >Fale com August</a
          >
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <h1>Calculadora Avançada de Necessidades Calóricas Diárias</h1>
        <p>Descubra quantas calorias você precisa diariamente com nossa calculadora precisa de calorias. Perfeita para perda de peso, ganho muscular, <br /> ou manter um estilo de vida saudável com base no seu corpo, objetivos e nível de atividade.</p>
        <section class="form-container">
          <div class="tabs">
            <button class="tab-button" data-unit="us">Unidades dos EUA</button>
            <button class="tab-button active" data-unit="metric">Unidades Métricas</button>
          </div>
          <p class="form-instruction">Insira seus dados pessoais abaixo e clique em Calcular para obter suas recomendações calóricas personalizadas</p>
          <form id="calorie-form">
            <div class="form-field">
              <label for="age">Idade</label>
              <input type="text" id="age" value="25" />
              <span>idades 15 - 80</span>
            </div>
            <div class="form-field">
              <label>Gênero</label>
              <input type="radio" name="gender" value="male" checked /> Masculino
              <input type="radio" name="gender" value="female" /> Feminino
            </div>
            <div class="form-field">
              <label for="height">Altura</label>
              <!-- US Units (feet and inches) -->
              <div id="height-us" style="display: none">
                <input
                  type="text"
                  id="height-feet"
                  value="5"
                  style="width: 60px"
                />
                <span>pés</span>
                <input
                  type="text"
                  id="height-inches"
                  value="10"
                  style="width: 60px"
                />
                <span>polegadas</span>
              </div>
              <!-- Metric Units (cm) -->
              <div id="height-metric" style="display: none">
                <input type="text" id="height-cm" value="180" />
                <span>cm</span>
              </div>
            </div>
            <div class="form-field">
              <label for="weight">Peso</label>
              <!-- US Units (pounds) -->
              <div id="weight-us" style="display: none">
                <input type="text" id="weight-lbs" value="165" />
                <span>libras</span>
              </div>
              <!-- Metric Units (kg) -->
              <div id="weight-metric" style="display: none">
                <input type="text" id="weight-kg" value="65" />
                <span>kg</span>
              </div>
            </div>
            <div class="form-field">
              <label for="activity">Atividade</label>
              <select id="activity">
                <option value="sedentary">Sedentário: pouco ou nenhum exercício</option>
                <option value="light">Levemente ativo: exercício leve 1-3 dias/semana</option>
                <option value="moderate" selected>Moderadamente ativo: exercício moderado 3-5 dias/semana</option>
                <option value="very">Muito ativo: exercício intenso 6-7 dias/semana</option>
                <option value="super">Super ativo: exercício muito intenso, trabalho físico</option>
              </select>
            </div>
            <div class="settings-link">
              <a href="#" id="settings-link">+ Configurações</a>
            </div>

            <!-- Settings Section (inline) -->
            <div id="settings-container" class="settings-container">
              <div class="settings-header">
                <h3>Configurações</h3>
                <button class="collapse-button" id="collapse-settings">
                  −
                </button>
              </div>

              <div class="settings-section">
                <h3>Unidade dos resultados:</h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="calories-unit"
                      name="results-unit"
                      value="calories"
                      checked
                    />
                    <label for="calories-unit">Calorias</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="kilojoules-unit"
                      name="results-unit"
                      value="kilojoules"
                    />
                    <label for="kilojoules-unit">Quilojoules</label>
                  </div>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Percentual de Gordura Corporal:
                  <span
                    class="info-icon"
                    title="Insira seu percentual de gordura corporal para cálculos mais precisos de composição corporal"
                    >?</span
                  >
                </h3>
                <div
                  class="body-fat-input"
                  style="display: flex; margin-left: 0"
                >
                  <input
                    type="number"
                    id="user-body-fat"
                    min="5"
                    max="50"
                    step="0.1"
                    value="20"
                  />
                  <span>%</span>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Fórmula de estimativa de BMR:
                  <span
                    class="info-icon"
                    title="Escolha a fórmula para calcular sua Taxa Metabólica Basal"
                    >?</span
                  >
                </h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="mifflin-formula"
                      name="bmr-formula"
                      value="mifflin"
                      checked
                    />
                    <label for="mifflin-formula">Mifflin St Jeor</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="harris-formula"
                      name="bmr-formula"
                      value="harris"
                    />
                    <label for="harris-formula">Harris-Benedict Revisada</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="katch-formula"
                      name="bmr-formula"
                      value="katch"
                    />
                    <label for="katch-formula">Katch-McArdle</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-buttons">
              <button type="button" class="calculate-button">
                Calcular ▶
              </button>
              <button type="button" class="clear-button">Limpar</button>
            </div>
          </form>
        </section>

        <!-- Enhanced Results Section -->
        <div id="results-container" class="results-container">
          <div class="results-header">
            <h2>Resultado</h2>
            <button
              class="download-btn"
              onclick="downloadResultsPDF()"
              title="Baixar resultados em PDF"
            >
              <span class="download-icon">📥</span>
              <span>Baixar PDF</span>
            </button>
          </div>
          <div class="results-content">
            <p class="results-description">Suas metas calóricas personalizadas são calculadas usando fórmulas metabólicas avançadas. Essas recomendações fornecem diretrizes de ingestão calórica diária adaptadas aos seus objetivos específicos - seja para manter seu peso atual, alcançar perda de peso sustentável ou apoiar ganho de peso saudável.</p>

            <!-- BMR and Activity Information - always hidden -->
            <div
              class="bmr-info-section"
              style="
                margin-bottom: 30px;
                display:none;
                padding: 20px;
                background-color: #f9fafb;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
              "
            >
              <h3
                style="
                  color: #416955;
                  font-size: 18px;
                  font-weight: 600;
                  margin-bottom: 16px;
                "
              >
                Taxa Metabólica Basal (BMR):
                <span id="bmr-value" style="color: #111827">1,650</span>
                calorias/dia
              </h3>

              <div style="margin-bottom: 16px">
                <h4
                  style="
                    color: #111827;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                  "
                >
                  Níveis de Atividade:
                </h4>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    font-size: 14px;
                  "
                >
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Sedentário:</strong> pouco ou nenhum exercício
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Leve:</strong> exercício 1-3 vezes/semana
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Moderado:</strong> exercício 4-5 vezes/semana
                  </div>
                  <div
                    id="active-highlight"
                    style="
                      padding: 8px;
                      background-color: #e0f2e7;
                      border-radius: 4px;
                      border: 2px solid #416955;
                    "
                  >
                    <strong>Ativo:</strong> exercício diário ou intenso 3-4 vezes/semana
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Muito Ativo:</strong> exercício intenso 6-7 vezes/semana
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Extra Ativo:</strong> exercício muito intenso diariamente, ou trabalho físico
                  </div>
                </div>
              </div>
            </div>

            <table class="results-table" id="results-table">
              <thead>
                <tr>
                  <th style="width: 40%">Objetivo</th>
                  <th style="width: 30%">Calorias</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-label="Objetivo">
                    <div class="goal-label">Manter peso</div>
                  </td>
                  <td data-label="Calorias Diárias">
                    <div class="calorie-value" id="maintain-calories">
                      2,549
                    </div>
                    <div class="percentage">100%</div>
                    <div class="unit-label">calorias/dia</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Objetivo">
                    <div class="goal-label">Perda de peso leve</div>
                    <div class="goal-description">0,5 lb/semana</div>
                  </td>
                  <td data-label="Calorias Diárias">
                    <div class="calorie-value" id="mild-loss-calories">
                      2,299
                    </div>
                    <div class="percentage">90%</div>
                    <div class="unit-label">calorias/dia</div>
                  </td>
                </tr>
                <tr>
                  <td data-label="Objetivo">
                    <div class="goal-label">Perda de peso</div>
                    <div class="goal-description">1 lb/semana</div>
                  </td>
                  <td data-label="Calorias Diárias">
                    <div class="calorie-value" id="weight-loss-calories">
                      2,049
                    </div>
                    <div class="percentage">80%</div>
                    <div class="unit-label">calorias/dia</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Objetivo">
                    <div class="goal-label">Perda de peso extrema</div>
                    <div class="goal-description">2 lb/semana</div>
                  </td>
                  <td data-label="Calorias Diárias">
                    <div class="calorie-value" id="extreme-loss-calories">
                      1,549
                    </div>
                    <div class="percentage">61%</div>
                    <div class="unit-label">calorias/dia</div>
                  </td>
                </tr>
              </tbody>
            </table>

            <div
              class="weight-gain-toggle"
              style="text-align: left; margin-top: 20px; padding-left: 0"
            >
              <a
                href="javascript:void(0)"
                class="toggle-link"
                id="weight-gain-toggle"
                >Mostrar informações para ganho de peso</a
              >
            </div>

            <div class="weight-gain-section" id="weight-gain-section">
              <h3 style="color: #416955; margin-bottom: 16px">
                Informações de Ganho de Peso
              </h3>
              <table class="results-table">
                <thead>
                  <tr>
                    <th style="width: 40%">Objetivo</th>
                    <th style="width: 30%">Calorias</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Objetivo">
                      <div class="goal-label">Ganho de peso leve</div>
                      <div class="goal-description">0,25 kg/semana</div>
                    </td>
                    <td data-label="Calorias Diárias">
                      <div class="calorie-value" id="mild-gain-calories">
                        2,799
                      </div>
                      <div class="percentage">112%</div>
                      <div class="unit-label">calorias/dia</div>
                    </td>
                  </tr>
                  <tr>
                    <td data-label="Objetivo">
                      <div class="goal-label">Ganho de peso</div>
                      <div class="goal-description">0,5 kg/semana</div>
                    </td>
                    <td data-label="Calorias Diárias">
                      <div class="calorie-value" id="weight-gain-calories">
                        3,049
                      </div>
                      <div class="percentage">124%</div>
                      <div class="unit-label">calorias/dia</div>
                    </td>
                  </tr>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Objetivo">
                      <div class="goal-label">Ganho de peso rápido</div>
                      <div class="goal-description">1 kg/semana</div>
                    </td>
                    <td data-label="Calorias Diárias">
                      <div class="calorie-value" id="fast-gain-calories">
                        3,549
                      </div>
                      <div class="percentage">148%</div>
                      <div class="unit-label">calorias/dia</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div id="result" style="display: none" class="result"></div>
        <div class="activity-definitions">
          <h2>Diretrizes de Atividade Física</h2>
          <ul>
            <li>
              <strong>Exercício Leve:</strong> 20-40 minutos de atividades de intensidade moderada como caminhada ou ioga leve.
            </li>
            <li>
              <strong>Exercício Moderado:</strong> 30-60 minutos de atividades que aumentam sua frequência cardíaca, como caminhada rápida ou ciclismo.
            </li>
            <li>
              <strong>Exercício Vigoroso:</strong> 45-90 minutos de treino de alta intensidade, esportes ou atividades físicas exigentes.
            </li>
            <li>
              <strong>Treinamento Profissional/Atlético:</strong> 2+ horas de treinamento intensivo ou trabalho ocupacional fisicamente exigente.
            </li>
          </ul>
        </div>

        <!-- Nutritional Reference Tables Section -->
        <section class="info-section">
          <h2>Guia Completo de Referência Nutricional</h2>
          <p>Use estas tabelas abrangentes para tomar decisões dietéticas informadas e entender melhor o conteúdo calórico de alimentos do dia a dia, estratégias de planejamento de refeições e gasto energético do exercício.</p>

          <!-- Food Calorie Table -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Conteúdo Calórico de Alimentos Populares
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 600px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Alimento
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Tamanho da Porção
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Calorias
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      kJ
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Frutas Frescas
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Manga
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 média (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      135
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      565
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Kiwi
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 grande (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      56
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      234
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mirtilos
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      84
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      351
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Abacate
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 média (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      160
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      670
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Cerejas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      97
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      406
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Vegetais Frescos
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Batata-doce
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 média (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      112
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      469
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Pimentão
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara fatiada
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      28
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      117
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Espinafre
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2 xícaras frescas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      14
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      59
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Abobrinha
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara fatiada
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      19
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      80
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Couve-flor
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      25
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      105
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Fontes de Proteína
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Salmão, grelhado
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      175
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      732
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Peito de peru
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Iogurte grego
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Pote de 6 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      130
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      544
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Amêndoas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 oz. (23 unidades)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      164
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      686
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Lentilhas, cozidas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 xícara
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      115
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      481
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Grãos & Amidos
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Quinoa, cozida
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      222
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      929
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Aveia
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara cozida
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      154
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      644
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Macarrão integral
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara cozida
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      174
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      728
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Arroz integral
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara cozida
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      218
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      912
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Bebidas
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Chá verde
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      8
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Leite de amêndoas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      39
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      163
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Água de coco
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 xícara
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      46
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      192
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Vinho tinto
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      5 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p style="font-size: 12px; color: #6b7280; font-style: italic">
              Nota: Os valores calóricos são aproximados e podem variar conforme métodos de preparo e marcas específicas.
            </p>
          </div>

          <!-- Sample Meal Plans -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Planejamento Estratégico de Refeições
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 700px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Período da Refeição
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Plano de 1.300 Calorias
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Plano de 1.600 Calorias
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Plano de 2.100 Calorias
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Café da manhã
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 xícara de iogurte grego com 1/2 xícara de mirtilos (130 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 xícara de aveia com 1/2 xícara de mirtilos (238 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 xícara de aveia com 1 kiwi, 1 oz. de amêndoas (458 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Lanche da Manhã
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 kiwi pequeno (56 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 manga média (135 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 manga média, 10 cerejas (232 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total da Manhã
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      186 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      373 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      690 cal
                    </td>
                  </tr>

                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Almoço
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      2 xícaras de salada de espinafre com 3 oz. de salmão grelhado (189 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de peito de peru, 1 xícara de abobrinha, 1/2 xícara de quinoa (264 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de salmão grelhado, 1 xícara de arroz integral, 1 xícara de couve-flor (418 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Lanche da Tarde
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 xícara de pimentão fatiado (28 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 abacate (160 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 abacate, 1 oz. de amêndoas (324 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total do Meio-dia
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      217 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      424 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      742 cal
                    </td>
                  </tr>

                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Jantar
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de peito de peru, 1 xícara de couve-flor (150 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de salmão grelhado, 1 xícara de batata-doce (287 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de peito de peru, 1 xícara de macarrão integral, 1 xícara de espinafre (313 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Lanche da Noite
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 xícara de chá verde (2 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 xícara de água de coco (46 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 xícara de iogurte grego (130 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total da Noite
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      152 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      333 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      443 cal
                    </td>
                  </tr>
                  <tr style="background-color: #416955; color: white">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Total Diário
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1.255 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1.630 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      2.175 cal
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Comprehensive Information Section -->
        <section class="info-section">
          <h2>Domine Seu Metabolismo</h2>
          <p>Entender seu metabolismo é fundamental para alcançar seus objetivos de saúde. Esta calculadora usa fórmulas cientificamente validadas para estimar sua Taxa Metabólica Basal (BMR) e o Gasto Energético Total Diário (TDEE).</p>

          <div class="equations-container">
            <h3>Três Fórmulas Comprovadas para BMR</h3>
            <p>Esta calculadora utiliza três equações bem pesquisadas para estimar seu BMR, cada uma com pontos fortes únicos dependendo do seu perfil:</p>

            <div class="equation-card">
              <h4>Equação de Mifflin-St Jeor</h4>
              <p>
                <strong>Masculino:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) + 5
              </p>
              <p>
                <strong>Feminino:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) - 161
              </p>
              <p class="equation-note">
                Considerada a mais precisa para a população geral, especialmente para não atletas.
              </p>
            </div>

            <div class="equation-card">
              <h4>Equação Harris-Benedict Revisada</h4>
              <p>
                <strong>Masculino:</strong> BMR = 13.397 × weight + 4.799
                × height - 5.677 × age + 88.362
              </p>
              <p>
                <strong>Feminino:</strong> BMR = 9.247 × weight +
                3.098 × height - 4.330 × age + 447.593
              </p>
              <p class="equation-note">
                Uma fórmula confiável atualizada para precisão moderna, adequada para uma ampla gama de indivíduos.
              </p>
            </div>

            <div class="equation-card">
              <h4>Fórmula Katch-McArdle</h4>
              <p>BMR = 370 + 21.6 × (1 − body fat percentage) × weight (kg)</p>
              <p class="equation-note">
                Ideal para indivíduos com percentual de gordura corporal conhecido, pois considera a massa magra.
              </p>
            </div>

            <div class="info-text">
              <h3>TDEE: Transformando BMR em Metas Atingíveis</h3>
              <p>Seu Gasto Energético Total Diário (TDEE) é seu BMR multiplicado por um fator de atividade que reflete seu estilo de vida. Isso fornece uma visão completa de suas necessidades calóricas diárias.</p>

              <div
                class="activity-table"
                style="
                  margin: 20px 0;
                  background-color: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  padding: 20px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #416955; color: white">
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Nível de Atividade
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Descrição
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Multiplicador
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Sedentário
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        pouco ou nenhum exercício
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.2
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Levemente Ativo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        exercício 1-3 vezes/semana
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.375
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Moderadamente Ativo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        exercício 4-5 vezes/semana
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.55
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Muito Ativo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        exercício intenso 6-7 vezes/semana
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.725
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Super Ativo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        exercício muito intenso diariamente, ou trabalho físico
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.9
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p>O resultado é uma estimativa precisa das calorias que você queima diariamente, que pode ser usada para adaptar sua dieta para perda de peso, manutenção ou ganho.</p>

              <h3>Gestão Estratégica de Peso</h3>
              <p>Para perder peso, é necessário um déficit calórico; para ganhar peso, um superávit. Esta calculadora fornece metas calóricas precisas para cada objetivo, garantindo progresso sustentável.</p>

              <div class="warning-note">
                <strong>Diretrizes Críticas para Perda de Peso Segura:</strong>
                <ul style="margin: 8px 0; padding-left: 20px">
                  <li>Evite déficits extremos para não desacelerar seu metabolismo.</li>
                  <li>Garanta ingestão adequada de proteína para preservar a massa muscular.</li>
                  <li>Priorize alimentos ricos em nutrientes para evitar deficiências.</li>
                  <li>Evite dietas radicais para prevenir o reganho de peso.</li>
                </ul>
                <p style="margin-top: 12px">Para resultados ideais, consulte um nutricionista ou dietista para personalizar ainda mais seu plano.</p>
              </div>

              <h3>Estratégias de Otimização Nutricional</h3>
              <ul
                style="
                  list-style-type: disc;
                  padding-left: 20px;
                  margin: 16px 0;
                "
              >
                <li>Priorize alimentos ricos em nutrientes como vegetais, proteínas magras e grãos integrais.</li>
                <li>Garanta ingestão adequada de proteína para apoiar a manutenção muscular e a saciedade.</li>
                <li>Rejeite dietas excessivamente restritivas em favor de uma alimentação equilibrada e sustentável.</li>
                <li>Acompanhe sua ingestão consistentemente para criar hábitos saudáveis a longo prazo.</li>
              </ul>

              <p style="font-weight: 500; color: #416955; margin-top: 20px">
                Esta calculadora é um ponto de partida. Ajuste com base no seu progresso e consulte profissionais para orientação personalizada.
              </p>
            </div>
          </div>
        </section>

        <!-- Calorie Counting Section -->
        <section class="counting-section">
          <h2>Rastreamento Preciso de Calorias</h2>

          <div class="steps-container">
            <div class="step-card">
              <h4>Passo 1: Determine Sua Linha de Base Metabólica</h4>
              <p>Use esta calculadora para encontrar seu BMR e TDEE com base em sua idade, gênero, peso, altura e nível de atividade.</p>
            </div>

            <div class="step-card">
              <h4>Passo 2: Estabeleça Suas Metas de Peso</h4>
              <p>Defina metas realistas para perda de peso, manutenção ou ganho, usando as recomendações calóricas fornecidas.</p>
            </div>

            <div class="step-card">
              <h4>Passo 3: Implemente Sistemas de Monitoramento</h4>
              <p>Acompanhe sua ingestão calórica usando aplicativos ou um diário alimentar para manter-se alinhado às suas metas.</p>
              <p>Pese-se semanalmente e monitore tendências, não flutuações diárias.</p>
            </div>

            <div class="step-card">
              <h4>Passo 4: Otimize Através da Avaliação</h4>
              <p>Reavalie suas necessidades calóricas a cada 4-6 semanas ou após mudanças significativas de peso para manter seu plano eficaz.</p>
            </div>
          </div>

          <div class="info-text">
            <h3>A Ciência do Balanço Calórico</h3>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li><strong>Fundamentos do Balanço Energético:</strong> O gerenciamento de peso é regido pelas calorias consumidas versus calorias gastas.</li>
              <li><strong>Efeito Térmico dos Alimentos:</strong> Alimentos diferentes exigem diferentes quantidades de energia para serem digeridos, impactando o total de calorias queimadas.</li>
              <li><strong>Saciedade e Qualidade dos Alimentos:</strong> Alimentos ricos em fibras e proteínas promovem saciedade, ajudando você a seguir seu plano.</li>
            </ul>

            <p style="font-style: italic; color: #374151; margin: 16px 0">
              Exemplo: A 'Dieta Twinkie' mostrou que a perda de peso é possível com déficit calórico, mas dietas pobres em nutrientes prejudicam a saúde a longo prazo.
            </p>

            <h4 style="color: #416955; margin-top: 20px">Benefícios Extras do Rastreamento de Calorias</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Constrói consciência nutricional e hábitos alimentares conscientes.</li>
              <li>Melhora o controle de porções por meio do rastreamento consistente.</li>
              <li>Conecta escolhas alimentares ao exercício para otimizar o balanço energético.</li>
            </ul>
          </div>
        </section>

        <!-- Zigzag Calorie Cycling Section -->
        <section class="zigzag-section">
          <h2>Ciclagem de Calorias Zigzag</h2>
          <p>A ciclagem de calorias zigzag envolve variar sua ingestão calórica diária enquanto mantém sua meta semanal para melhorar a flexibilidade metabólica e evitar platôs.</p>

          <div class="zigzag-explanation">
            <div class="example-card">
              <h4>Exemplo</h4>
              <p><strong>Meta Semanal:</strong> 14.000 calorias (2.000 calorias/dia em média)</p>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li><strong>Opção A:</strong> 7 dias a 2.000 calorias.</li>
                <li><strong>Opção B:</strong> 5 dias a 1.800 calorias, 2 dias a 2.500 calorias.</li>
              </ul>
              <p>Ambas as opções atingem a meta semanal, mas variam a ingestão diária para manter seu metabolismo dinâmico.</p>
            </div>

            <div class="benefits-card">
              <h4>Meta Zigzag</h4>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li>Melhor flexibilidade metabólica e adesão.</li>
                <li>Mais flexibilidade no planejamento de refeições, especialmente para eventos sociais.</li>
                <li>Previne adaptação metabólica de déficits prolongados.</li>
                <li>Quebra platôs de perda de peso variando a ingestão calórica.</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Calorie Requirements Section -->
        <section class="requirements-section">
          <h2>Necessidades Calóricas por Estilo de Vida</h2>
          <p>As necessidades calóricas variam por fatores individuais, mas diretrizes gerais podem ajudar você a começar.</p>

          <div class="factors-grid">
            <div class="factor-card">
              <h4>Fatores que Influenciam as Necessidades Calóricas</h4>
              <ul>
                <li>Idade, sexo, peso e altura.</li>
                <li>Nível de atividade (sedentário a altamente ativo).</li>
                <li>Estado de saúde, incluindo gravidez ou condições médicas.</li>
              </ul>
            </div>

            <div class="guidelines-card">
              <h4>Diretrizes Gerais</h4>
              <p><strong>Homens:</strong> 2.000–3.000 calorias/dia</p>
              <p><strong>Mulheres:</strong> 1.600–2.400 calorias/dia</p>
            </div>

            <div class="minimum-card">
              <h4>Ingestão Mínima Segura</h4>
              <p><strong>Mulheres:</strong> 1.200 calorias/dia</p>
              <p><strong>Homens:</strong> 1.500 calorias/dia</p>
              <p class="warning">Ingestões abaixo desses níveis devem ser supervisionadas por um médico.</p>
            </div>
          </div>

          <div class="warning-note" style="margin-top: 20px">
            <p>Restringir demais as calorias pode levar a deficiências nutricionais, perda muscular e desaceleração metabólica. Sempre priorize a saúde.</p>
          </div>
        </section>

        <!-- Calorie Types Section -->
        <section class="types-section">
          <h2>Nem Todas as Calorias São Iguais</h2>
          <p>As calorias vêm de diferentes macronutrientes, cada um com efeitos únicos no seu corpo:</p>
          <ul style="list-style-type: disc; padding-left: 20px; margin: 16px 0">
            <li>Proteína: 4 calorias/grama – apoia a reparação muscular e a saciedade.</li>
            <li>Carboidratos: 4 calorias/grama – principal fonte de energia.</li>
            <li>Gordura: 9 calorias/grama – essencial para hormônios e absorção de nutrientes.</li>
            <li>Álcool: 7 calorias/grama – valor nutricional mínimo.</li>
          </ul>

          <p>Rótulos nutricionais fornecem contagens calóricas precisas, mas tamanhos de porção e métodos de preparo importam.</p>

          <div class="calorie-types">
            <div class="type-card">
              <h4>Alimentos Ricos em Calorias</h4>
              <p>Densos em calorias, muitas vezes devido a gorduras ou açúcares. Use com moderação para controle de peso.</p>
              <ul>
                <li>Abacates, óleos.</li>
                <li>Nozes e sementes.</li>
                <li>Alimentos fritos.</li>
                <li>Doces e lanches açucarados.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Alimentos com Baixas Calorias</h4>
              <ul>
                <li>Muitos vegetais (ex.: espinafre, abobrinha).</li>
                <li>Algumas frutas (ex.: frutas vermelhas).</li>
                <li>Proteínas magras (ex.: peru, peixe).</li>
                <li>Grãos integrais com moderação.</li>
                <li>Verduras de folhas para volume e nutrientes.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Calorias Vazias</h4>
              <ul>
                <li>Bebidas açucaradas (ex.: refrigerante).</li>
                <li>Lanches processados (ex.: batatas fritas, biscoitos).</li>
                <li>Açúcares adicionados em alimentos embalados.</li>
                <li>Gorduras sólidas (ex.: manteiga, margarina).</li>
                <li>Álcool com benefício nutricional mínimo.</li>
              </ul>
            </div>
          </div>

          <div class="thermic-effect">
            <h3>Por Que a Qualidade Calórica Importa</h3>
            <p>Bebidas como refrigerante ou álcool adicionam calorias sem saciedade, dificultando manter um déficit.</p>

            <h4 style="color: #416955; margin-top: 16px">Monte um Plano Balanceado</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Foque em alimentos integrais e não processados para densidade nutricional.</li>
              <li>Limite lanches e bebidas açucaradas.</li>
              <li>Use alimentos ricos em fibras e proteínas para controle natural de porções.</li>
              <li>Combine contagem de calorias com exercício para resultados sustentáveis.</li>
            </ul>
          </div>
        </section>

        <!-- Final Takeaway Section -->
        <section class="info-section" style="margin-top: 40px">
          <h2>Conclusão Final</h2>
          <p>Não existe uma abordagem única para nutrição. Use esta calculadora como ponto de partida, acompanhe seu progresso e ajuste conforme necessário para atender às suas necessidades únicas.</p>
          <p style="font-weight: 500; color: #416955">Acompanhe com sabedoria, coma com atenção e priorize a saúde a longo prazo.</p>
        </section>
      </main>
    </div>
    <script src="/pt/calculators/calorie-calculator/index.js"></script>
    <script>
    const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
    const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
    const select = document.getElementById('language-select');
    const currentLang = window.location.pathname.split('/')[1] || 'en';
    locales.forEach(l => {
      const opt = document.createElement('option');
      opt.value = l;
      opt.textContent = languageNames[l] || l;
      if (l === currentLang) opt.selected = true;
      select.appendChild(opt);
    });
    select.addEventListener('change', function() {
      const newLang = this.value;
      let path = window.location.pathname.split('/');
      if (locales.includes(path[1])) { path[1] = newLang; }
      else { path = ['', newLang, ...path.slice(1)]; }
      localStorage.setItem('preferredLang', newLang);
      window.location.pathname = path.join('/');
    });
    // Persist language choice
    if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
      select.value = localStorage.getItem('preferredLang');
      select.dispatchEvent(new Event('change'));
    }
    </script>
  </body>
</html>