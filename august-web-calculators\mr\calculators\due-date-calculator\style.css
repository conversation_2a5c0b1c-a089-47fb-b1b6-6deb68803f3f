
        :root {
            --primary-color: #206e55;
            --primary-dark: #1a5a47;
            --primary-light: #f0f9f4;
            --text-primary: #111827;
            --text-secondary: #374151;
            --background-light: #f9fafb;
            --background-white: #fff;
            --border-radius: 12px;
            --shadow-lg: 0 8px 24px rgba(0,0,0,0.08);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background-light);
            color: var(--text-primary);
            min-height: 100vh;
            line-height: 1.6;
        }

        header {
            background-color: transparent;
            padding: 8px 0px;
            border-bottom: none;
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: none;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo img {
            height: 60px;
            width: auto;
        }

        .nav {
            display: flex;
            align-items: center;
            gap: 32px;
        }

        .language-switcher {
            display: flex;
            align-items: center;
            gap: 8px;
            /* background: var(--background-white); */
            padding: 8px 12px;
            font-size: 14px;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .language-switcher:hover {
            background: var(--primary-light);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .language-switcher img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }

        .talk-to-august {
            background-color: #206e55;
            color: white !important;
            padding: 10px 18px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            font-size: 16px;
            transition: background-color 0.2s ease;
        }

        .talk-to-august:hover {
            background-color: #1a5a47;
            color: white !important;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .hero {
            text-align: center;
            margin-bottom: 48px;
        }

        .hero h1 {
            color: var(--primary-color);
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 12px;
        }

        .hero p {
            color: var(--text-secondary);
            font-size: 1.25rem;
            margin-bottom: 0;
        }

        .main-content {
            display: flex;
            gap: 32px;
            align-items: flex-start;
            justify-content: center;
        }

        .info-card {
            background: var(--background-white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            padding: 32px;
            max-width: 500px;
            width: 100%;
            border: 1px solid #e5e7eb;
            padding-bottom: 52px;
        }

        .info-card h2 {
            color: var(--primary-color);
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .info-card h3 {
            color: var(--primary-color);
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 12px;
            margin-top: 24px;
        }

        .info-card p {
            color: var(--text-secondary);
            margin-bottom: 16px;
            line-height: 1.7;
        }

        .info-card ul {
            color: var(--text-secondary);
            margin-bottom: 16px;
            padding-left: 20px;
        }

        .info-card li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .calculator-card {
            background: var(--background-white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            padding: 32px;
            max-width: 500px;
            width: 100%;
            color: var(--text-primary);
            border: 1px solid #e5e7eb;
        }

        .calculator-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 24px;
            text-align: center;
            color: var(--primary-color);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #111827;
            font-size: 1rem;
        }

        .date-inputs {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .date-inputs select {
            padding: 10px 14px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #fff;
            color: #111827;
            font-size: 16px;
            min-width: 70px;
            transition: border-color 0.2s ease;
            box-shadow: none;
        }

        .date-inputs select:focus {
            outline: none;
            border-color: #416955;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
        }

        .date-inputs select option {
            background: #fff;
            color: #111827;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }

        .btn {
            background: var(--primary-color);
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 10px 18px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn:hover {
            background: var(--primary-dark);
        }

        .btn:active {
            transform: translateY(0);
        }

        .results-section {
            background: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 24px;
            border: 1px solid #e5e7eb;
            backdrop-filter: none;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px;
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .result-item:last-child {
            margin-bottom: 0;
        }

        .result-label {
            font-weight: 600;
            color: #111827;
            font-size: 14px;
        }

        .result-value {
            background: #f9fafb;
            padding: 8px 12px;
            border-radius: 4px;
            color: #111827;
            font-weight: 500;
            min-width: 140px;
            text-align: center;
            border: 1px solid #e5e7eb;
        }

        .check-mark {
            color: var(--primary-color);
            font-weight: bold;
            margin-right: 8px;
        }

        .feature-list {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .feature-text {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .faq-section {
            background: var(--background-white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            padding: 32px;
            margin-top: 32px;
            border: 1px solid #e5e7eb;
        }

        .faq-section h2 {
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 24px;
            text-align: center;
        }

        .faq-item {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .faq-item:last-child {
            border-bottom: none;
        }

        .faq-question {
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .faq-answer {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        @media (max-width: 1024px) {
            .main-content {
                flex-direction: column;
                align-items: center;
            }
            
            .calculator-card,
            .info-card {
                max-width: 600px;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                padding: 16px 8px;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 20px 16px;
            }
            
            .date-inputs {
                justify-content: center;
            }
            
            .button-group {
                justify-content: center;
            }
            
            .result-item {
                flex-direction: column;
                text-align: center;
                gap: 8px;
            }
            
            .result-value {
                min-width: auto;
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .calculator-card,
            .info-card {
                padding: 20px;
            }
            
            .date-inputs select {
                min-width: 60px;
                padding: 8px;
            }

        }

        
header {
    background-color: transparent;
    padding: 8px 0px;
    border-bottom: none;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: none;
  }
  
  header.scrolled {
    background-color: #f8f9fa;
    padding: 4px 0px;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    /* max-width: 1430px; */
    margin: 0 auto;
  }
  .logo {
    display: flex;
    align-items: center;
  }
  .logo img {
    height: 60px;
    width: auto;
  }
  .nav {
    display: flex;
    align-items: center;
    gap: 32px;
  }
  .nav-links {
    display: flex;
    align-items: center;
    gap: 24px;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .nav-links a {
    color: #111827;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.2s ease;
  }
  .nav-links a:hover {
    color: #206e55;
  }
  .nav-links a.active {
    color: #206e55;
  }
  .talk-to-august {
    background-color: #206e55;
    color: white !important;
    padding: 10px 18px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: background-color 0.2s ease;
  }
  .talk-to-august:hover {
    background-color: #1a5a47;
    color: white !important;
  }