<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Calculadora de Necesidades Calóricas Diarias - Calculadora Precisa de TMB y GET | MeetAugust</title>
    <meta name="description" content="Calcula tus necesidades calóricas diarias con nuestra calculadora avanzada de TMB. Obtén objetivos calóricos personalizados para pérdida de peso, mantenimiento y ganancia muscular. Gratuita, precisa y basada en la ciencia." />
    <meta name="keywords" content="calculadora de calorías, calculadora TMB, calculadora GET, necesidades calóricas diarias, calculadora de pérdida de peso, calculadora de metabolismo, planificación nutricional" />
    <meta name="author" content="MeetAugust" />
    <meta property="og:title" content="Calculadora de Necesidades Calóricas Diarias - Calculadora Precisa de TMB y GET" />
    <meta property="og:description" content="Calcula tus necesidades calóricas diarias con nuestra calculadora avanzada de TMB. Obtén objetivos calóricos personalizados para pérdida de peso, mantenimiento y ganancia muscular." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Calculadora de Necesidades Calóricas Diarias - Calculadora Precisa de TMB y GET" />
    <meta name="twitter:description" content="Calcula tus necesidades calóricas diarias con nuestra calculadora avanzada de TMB. Obtén objetivos calóricos personalizados para pérdida de peso, mantenimiento y ganancia muscular." />
    <link rel="canonical" href="https://www.meetaugust.ai/calculator/calorie" />
    
    <link rel="canonical" href="https://www.meetaugust.ai/es/calculators/calorie-calculator" />
    <link rel="icon" href="/es/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/es/calculators/calorie-calculator/style.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/es/calculators/">
            <img
          width="200"
          src="/es/calculators/assets/august_logo_green_nd4fn9.svg"
          alt="Logo de Calculadora"
        />
        </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=calorie_cal_topnav"
            class="talk-to-august"
            >Habla con August</a
          >
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <h1>Calculadora Avanzada de Requisitos Calóricos Diarios</h1>
        <p>Descubre cuántas calorías necesitas diariamente con nuestra calculadora precisa de calorías. Perfecta para pérdida de peso, ganancia muscular, <br /> o mantener un estilo de vida saludable basado en tu cuerpo, objetivos y nivel de actividad.</p>
        <section class="form-container">
          <div class="tabs">
            <button class="tab-button" data-unit="us">Unidades EE.UU.</button>
            <button class="tab-button active" data-unit="metric">Unidades Métricas</button>
          </div>
          <p class="form-instruction">Ingresa tus datos personales a continuación y haz clic en Calcular para obtener tus recomendaciones calóricas personalizadas</p>
          <form id="calorie-form">
            <div class="form-field">
              <label for="age">Edad</label>
              <input type="text" id="age" value="25" />
              <span>edades 15 - 80</span>
            </div>
            <div class="form-field">
              <label>Género</label>
              <input type="radio" name="gender" value="male" checked /> Masculino
              <input type="radio" name="gender" value="female" /> Femenino
            </div>
            <div class="form-field">
              <label for="height">Altura</label>
              <!-- US Units (feet and inches) -->
              <div id="height-us" style="display: none">
                <input
                  type="text"
                  id="height-feet"
                  value="5"
                  style="width: 60px"
                />
                <span>pies</span>
                <input
                  type="text"
                  id="height-inches"
                  value="10"
                  style="width: 60px"
                />
                <span>pulgadas</span>
              </div>
              <!-- Metric Units (cm) -->
              <div id="height-metric" style="display: none">
                <input type="text" id="height-cm" value="180" />
                <span>cm</span>
              </div>
            </div>
            <div class="form-field">
              <label for="weight">Peso</label>
              <!-- US Units (pounds) -->
              <div id="weight-us" style="display: none">
                <input type="text" id="weight-lbs" value="165" />
                <span>libras</span>
              </div>
              <!-- Metric Units (kg) -->
              <div id="weight-metric" style="display: none">
                <input type="text" id="weight-kg" value="65" />
                <span>kg</span>
              </div>
            </div>
            <div class="form-field">
              <label for="activity">Actividad</label>
              <select id="activity">
                <option value="sedentary">Sedentario: poco o ningún ejercicio</option>
                <option value="light">Ligeramente activo: ejercicio ligero 1-3 días/semana</option>
                <option value="moderate" selected>Moderadamente activo: ejercicio moderado 3-5 días/semana</option>
                <option value="very">Muy activo: ejercicio intenso 6-7 días/semana</option>
                <option value="super">Súper activo: ejercicio muy intenso, trabajo físico</option>
              </select>
            </div>
            <div class="settings-link">
              <a href="#" id="settings-link">+ Configuración</a>
            </div>

            <!-- Settings Section (inline) -->
            <div id="settings-container" class="settings-container">
              <div class="settings-header">
                <h3>Configuración</h3>
                <button class="collapse-button" id="collapse-settings">
                  −
                </button>
              </div>

              <div class="settings-section">
                <h3>Unidad de resultados:</h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="calories-unit"
                      name="results-unit"
                      value="calories"
                      checked
                    />
                    <label for="calories-unit">Calorías</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="kilojoules-unit"
                      name="results-unit"
                      value="kilojoules"
                    />
                    <label for="kilojoules-unit">Kilojulios</label>
                  </div>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Porcentaje de Grasa Corporal:
                  <span
                    class="info-icon"
                    title="Ingresa tu porcentaje de grasa corporal para cálculos más precisos de composición corporal"
                    >?</span
                  >
                </h3>
                <div
                  class="body-fat-input"
                  style="display: flex; margin-left: 0"
                >
                  <input
                    type="number"
                    id="user-body-fat"
                    min="5"
                    max="50"
                    step="0.1"
                    value="20"
                  />
                  <span>%</span>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Fórmula de estimación TMB:
                  <span
                    class="info-icon"
                    title="Elige la fórmula para calcular tu Tasa Metabólica Basal"
                    >?</span
                  >
                </h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="mifflin-formula"
                      name="bmr-formula"
                      value="mifflin"
                      checked
                    />
                    <label for="mifflin-formula">Mifflin St Jeor</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="harris-formula"
                      name="bmr-formula"
                      value="harris"
                    />
                    <label for="harris-formula">Harris-Benedict Revisada</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="katch-formula"
                      name="bmr-formula"
                      value="katch"
                    />
                    <label for="katch-formula">Katch-McArdle</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-buttons">
              <button type="button" class="calculate-button">
                Calcular ▶
              </button>
              <button type="button" class="clear-button">Limpiar</button>
            </div>
          </form>
        </section>

        <!-- Enhanced Results Section -->
        <div id="results-container" class="results-container">
          <div class="results-header">
            <h2>Resultado</h2>
            <button
              class="download-btn"
              onclick="downloadResultsPDF()"
              title="Descargar resultados como PDF"
            >
              <span class="download-icon">📥</span>
              <span>Descargar PDF</span>
            </button>
          </div>
          <div class="results-content">
            <p class="results-description">Tus objetivos calóricos personalizados se calculan usando fórmulas metabólicas avanzadas. Estas recomendaciones proporcionan pautas de ingesta calórica diaria adaptadas a tus objetivos específicos - ya sea que quieras mantener tu peso actual, lograr una pérdida de peso sostenible, o apoyar una ganancia de peso saludable.</p>

            <!-- BMR and Activity Information - always hidden -->
            <div
              class="bmr-info-section"
              style="
                margin-bottom: 30px;
                display:none;
                padding: 20px;
                background-color: #f9fafb;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
              "
            >
              <h3
                style="
                  color: #416955;
                  font-size: 18px;
                  font-weight: 600;
                  margin-bottom: 16px;
                "
              >
                Tasa Metabólica Basal (TMB):
                <span id="bmr-value" style="color: #111827">1,650</span>
                calorías/día
              </h3>

              <div style="margin-bottom: 16px">
                <h4
                  style="
                    color: #111827;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                  "
                >
                  Niveles de Actividad:
                </h4>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    font-size: 14px;
                  "
                >
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Sedentario:</strong> poco o ningún ejercicio
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Ligero:</strong> ejercicio 1-3 veces/semana
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Moderado:</strong> ejercicio 4-5 veces/semana
                  </div>
                  <div
                    id="active-highlight"
                    style="
                      padding: 8px;
                      background-color: #e0f2e7;
                      border-radius: 4px;
                      border: 2px solid #416955;
                    "
                  >
                    <strong>Activo:</strong> ejercicio diario o ejercicio intenso 3-4 veces/semana
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Muy Activo:</strong> ejercicio intenso 6-7 veces/semana
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Extra Activo:</strong> ejercicio muy intenso diariamente, o trabajo físico
                  </div>
                </div>
              </div>
            </div>

            <table class="results-table" id="results-table">
              <thead>
                <tr>
                  <th style="width: 40%">Objetivo</th>
                  <th style="width: 30%">Calorías</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-label="Objetivo">
                    <div class="goal-label">Mantener peso</div>
                  </td>
                  <td data-label="Calorías Diarias">
                    <div class="calorie-value" id="maintain-calories">
                      2,549
                    </div>
                    <div class="percentage">100%</div>
                    <div class="unit-label">calorías/día</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Objetivo">
                    <div class="goal-label">Pérdida leve de peso</div>
                    <div class="goal-description">0.5 lb/semana</div>
                  </td>
                  <td data-label="Calorías Diarias">
                    <div class="calorie-value" id="mild-loss-calories">
                      2,299
                    </div>
                    <div class="percentage">90%</div>
                    <div class="unit-label">calorías/día</div>
                  </td>
                </tr>
                <tr>
                  <td data-label="Objetivo">
                    <div class="goal-label">Pérdida de peso</div>
                    <div class="goal-description">1 lb/semana</div>
                  </td>
                  <td data-label="Calorías Diarias">
                    <div class="calorie-value" id="weight-loss-calories">
                      2,049
                    </div>
                    <div class="percentage">80%</div>
                    <div class="unit-label">calorías/día</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Objetivo">
                    <div class="goal-label">Pérdida extrema de peso</div>
                    <div class="goal-description">2 lb/semana</div>
                  </td>
                  <td data-label="Calorías Diarias">
                    <div class="calorie-value" id="extreme-loss-calories">
                      1,549
                    </div>
                    <div class="percentage">61%</div>
                    <div class="unit-label">calorías/día</div>
                  </td>
                </tr>
              </tbody>
            </table>

            <div
              class="weight-gain-toggle"
              style="text-align: left; margin-top: 20px; padding-left: 0"
            >
              <a
                href="javascript:void(0)"
                class="toggle-link"
                id="weight-gain-toggle"
                >Mostrar información para ganancia de peso</a
              >
            </div>

            <div class="weight-gain-section" id="weight-gain-section">
              <h3 style="color: #416955; margin-bottom: 16px">
                Información de Ganancia de Peso
              </h3>
              <table class="results-table">
                <thead>
                  <tr>
                    <th style="width: 40%">Objetivo</th>
                    <th style="width: 30%">Calorías</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Objetivo">
                      <div class="goal-label">Ganancia leve de peso</div>
                      <div class="goal-description">0.25 kg/semana</div>
                    </td>
                    <td data-label="Calorías Diarias">
                      <div class="calorie-value" id="mild-gain-calories">
                        2,799
                      </div>
                      <div class="percentage">112%</div>
                      <div class="unit-label">calorías/día</div>
                    </td>
                  </tr>
                  <tr>
                    <td data-label="Objetivo">
                      <div class="goal-label">Ganancia de peso</div>
                      <div class="goal-description">0.5 kg/semana</div>
                    </td>
                    <td data-label="Calorías Diarias">
                      <div class="calorie-value" id="weight-gain-calories">
                        3,049
                      </div>
                      <div class="percentage">124%</div>
                      <div class="unit-label">calorías/día</div>
                    </td>
                  </tr>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Objetivo">
                      <div class="goal-label">Ganancia rápida de peso</div>
                      <div class="goal-description">1 kg/semana</div>
                    </td>
                    <td data-label="Calorías Diarias">
                      <div class="calorie-value" id="fast-gain-calories">
                        3,549
                      </div>
                      <div class="percentage">148%</div>
                      <div class="unit-label">calorías/día</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div id="result" style="display: none" class="result"></div>
        <div class="activity-definitions">
          <h2>Pautas de Actividad Física</h2>
          <ul>
            <li>
              <strong>Ejercicio Ligero:</strong> 20-40 minutos de actividades de intensidad moderada como caminar o yoga suave.
            </li>
            <li>
              <strong>Ejercicio Moderado:</strong> 30-60 minutos de actividades que elevan tu frecuencia cardíaca, como caminar rápido o ciclismo.
            </li>
            <li>
              <strong>Ejercicio Vigoroso:</strong> 45-90 minutos de entrenamiento de alta intensidad, deportes, o actividades físicas exigentes.
            </li>
            <li>
              <strong>Entrenamiento Profesional/Atlético:</strong> 2+ horas de entrenamiento intensivo o trabajo ocupacional físicamente exigente.
            </li>
          </ul>
        </div>

        <!-- Nutritional Reference Tables Section -->
        <section class="info-section">
          <h2>Guía Completa de Referencia Nutricional</h2>
          <p>Usa estas tablas completas para tomar decisiones dietéticas informadas y entender mejor el contenido calórico de alimentos cotidianos, estrategias de planificación de comidas, y gasto energético del ejercicio.</p>

          <!-- Food Calorie Table -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Contenido Calórico de Alimentos Populares
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 600px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Alimento
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Tamaño de Porción
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Calorías
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      kJ
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Frutas Frescas
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mango
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 mediano (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      135
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      565
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Kiwi
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 grande (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      56
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      234
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Arándanos
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      84
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      351
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Aguacate
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 mediano (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      160
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      670
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Cerezas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      97
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      406
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Verduras Frescas
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Batata
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 mediana (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      112
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      469
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Pimiento
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza en rodajas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      28
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      117
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Espinaca
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2 tazas frescas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      14
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      59
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Calabacín
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza en rodajas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      19
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      80
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Coliflor
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      25
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      105
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Fuentes de Proteína
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Salmón, a la parrilla
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      175
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      732
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Pechuga de pavo
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Yogur griego
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Contenedor de 6 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      130
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      544
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Almendras
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 oz. (23 nueces)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      164
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      686
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Lentejas, cocidas
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 taza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      115
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      481
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Granos y Almidones
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Quinoa, cocida
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      222
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      929
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Avena
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza cocida
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      154
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      644
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Pasta integral
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza cocida
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      174
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      728
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Arroz integral
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza cocido
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      218
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      912
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Bebidas
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Té verde
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      8
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Leche de almendras
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      39
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      163
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Agua de coco
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 taza
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      46
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      192
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Vino tinto
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      5 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p style="font-size: 12px; color: #6b7280; font-style: italic">
              Nota: Los valores calóricos son aproximados y pueden variar según los métodos de preparación y marcas específicas.
            </p>
          </div>

          <!-- Sample Meal Plans -->
          <div
            class="nutrition-table-container"
            style="
              text-align: center;
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Planificación Estratégica de Comidas
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 700px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Período de Comida
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Plan de 1,300 Calorías
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Plan de 1,600 Calorías
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: center;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Plan de 2,100 Calorías
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Desayuno
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 taza de yogur griego con 1/2 taza de arándanos (130 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 taza de avena con 1/2 taza de arándanos (238 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 taza de avena con 1 kiwi, 1 oz. de almendras (458 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Refrigerio Matutino
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 kiwi pequeño (56 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 mango mediano (135 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 mango mediano, 10 cerezas (232 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total Matutino
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      186 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      373 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      690 cal
                    </td>
                  </tr>

                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Almuerzo
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      2 tazas de ensalada de espinaca con 3 oz. de salmón a la parrilla (189 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de pechuga de pavo, 1 taza de calabacín, 1/2 taza de quinoa (264 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de salmón a la parrilla, 1 taza de arroz integral, 1 taza de coliflor (418 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Refrigerio Vespertino
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 taza de rodajas de pimiento (28 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 aguacate (160 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1/2 aguacate, 1 oz. de almendras (324 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total Mediodía
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      217 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      424 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      742 cal
                    </td>
                  </tr>

                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Cena
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de pechuga de pavo, 1 taza de coliflor (150 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de salmón a la parrilla, 1 taza de batata (287 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      3 oz. de pechuga de pavo, 1 taza de pasta integral, 1 taza de espinaca (313 cal)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Refrigerio Nocturno
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 taza de té verde (2 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 taza de agua de coco (46 cal)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      1 taza de yogur griego (130 cal)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total Nocturno
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      152 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      333 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      443 cal
                    </td>
                  </tr>
                  <tr style="background-color: #416955; color: white">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Total Diario
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1,255 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1,630 cal
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      2,175 cal
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Comprehensive Information Section -->
        <section class="info-section">
          <h2>Domina Tu Metabolismo</h2>
          <p>Entender tu metabolismo es clave para lograr tus objetivos de salud. Esta calculadora usa fórmulas científicamente validadas para estimar tu Tasa Metabólica Basal (TMB) y Gasto Energético Total Diario (GET).</p>

          <div class="equations-container">
            <h3>Tres Fórmulas Probadas para TMB</h3>
            <p>Esta calculadora emplea tres ecuaciones bien investigadas para estimar tu TMB, cada una con fortalezas únicas dependiendo de tu perfil:</p>

            <div class="equation-card">
              <h4>Ecuación de Mifflin-St Jeor</h4>
              <p>
                <strong>Masculino:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) + 5
              </p>
              <p>
                <strong>Femenino:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) - 161
              </p>
              <p class="equation-note">
                Ampliamente considerada como la más precisa para la población general, especialmente para no atletas.
              </p>
            </div>

            <div class="equation-card">
              <h4>Ecuación Revisada de Harris-Benedict</h4>
              <p>
                <strong>Masculino:</strong> BMR = 13.397 × weight + 4.799
                × height - 5.677 × age + 88.362
              </p>
              <p>
                <strong>Femenino:</strong> BMR = 9.247 × weight +
                3.098 × height - 4.330 × age + 447.593
              </p>
              <p class="equation-note">
                Una fórmula confiable actualizada para precisión moderna, adecuada para una amplia gama de individuos.
              </p>
            </div>

            <div class="equation-card">
              <h4>Fórmula de Katch-McArdle</h4>
              <p>BMR = 370 + 21.6 × (1 − body fat percentage) × weight (kg)</p>
              <p class="equation-note">
                Ideal para individuos con porcentajes de grasa corporal conocidos, ya que considera la masa corporal magra.
              </p>
            </div>

            <div class="info-text">
              <h3>GET: Transformando TMB en Objetivos Accionables</h3>
              <p>Tu Gasto Energético Total Diario (GET) es tu TMB multiplicado por un factor de actividad que refleja tu estilo de vida. Esto te da una imagen completa de tus necesidades calóricas diarias.</p>

              <div
                class="activity-table"
                style="
                  margin: 20px 0;
                  background-color: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  padding: 20px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #416955; color: white">
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Nivel de Actividad
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Descripción
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Multiplicador
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Sedentario
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        poco o ningún ejercicio
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.2
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Ligeramente Activo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        ejercicio 1-3 veces/semana
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.375
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Moderadamente Activo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        ejercicio 4-5 veces/semana
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.55
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Muy Activo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        ejercicio intenso 6-7 veces/semana
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.725
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Súper Activo
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        ejercicio muy intenso diariamente, o trabajo físico
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.9
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p>El resultado es una estimación precisa de las calorías que quemas diariamente, que puedes usar para adaptar tu dieta para pérdida de peso, mantenimiento o ganancia.</p>

              <h3>Gestión Estratégica del Peso</h3>
              <p>Para perder peso, necesitas un déficit calórico; para ganar peso, un superávit. Esta calculadora proporciona objetivos calóricos precisos para cada objetivo, asegurando progreso sostenible.</p>

              <div class="warning-note">
                <strong>Pautas Críticas para Pérdida Segura de Peso:</strong>
                <ul style="margin: 8px 0; padding-left: 20px">
                  <li>Evita déficits extremos para prevenir ralentizar tu metabolismo.</li>
                  <li>Asegura proteína adecuada para preservar masa muscular.</li>
                  <li>Prioriza alimentos ricos en nutrientes para evitar deficiencias.</li>
                  <li>Evita dietas extremas para prevenir recuperación de peso.</li>
                </ul>
                <p style="margin-top: 12px">Para resultados óptimos, consulta a un nutricionista o dietista para personalizar tu plan aún más.</p>
              </div>

              <h3>Estrategias de Optimización Nutricional</h3>
              <ul
                style="
                  list-style-type: disc;
                  padding-left: 20px;
                  margin: 16px 0;
                "
              >
                <li>Prioriza alimentos ricos en nutrientes como verduras, proteínas magras y granos enteros.</li>
                <li>Asegura ingesta adecuada de proteína para apoyar mantenimiento muscular y saciedad.</li>
                <li>Rechaza dietas excesivamente restrictivas a favor de alimentación sostenible y equilibrada.</li>
                <li>Rastrea tu ingesta consistentemente para construir hábitos saludables a largo plazo.</li>
              </ul>

              <p style="font-weight: 500; color: #416955; margin-top: 20px">
                Esta calculadora es un punto de partida. Ajusta basado en tu progreso y consulta profesionales para orientación personalizada.
              </p>
            </div>
          </div>
        </section>

        <!-- Calorie Counting Section -->
        <section class="counting-section">
          <h2>Seguimiento Preciso de Calorías</h2>

          <div class="steps-container">
            <div class="step-card">
              <h4>Paso 1: Determina Tu Línea Base Metabólica</h4>
              <p>Usa esta calculadora para encontrar tu TMB y GET basado en tu edad, género, peso, altura y nivel de actividad.</p>
            </div>

            <div class="step-card">
              <h4>Paso 2: Establece Tus Objetivos de Peso</h4>
              <p>Establece objetivos realistas para pérdida de peso, mantenimiento o ganancia, usando las recomendaciones calóricas proporcionadas.</p>
            </div>

            <div class="step-card">
              <h4>Paso 3: Implementa Sistemas de Monitoreo</h4>
              <p>Rastrea tu ingesta calórica usando aplicaciones o un diario de alimentos para mantenerte alineado con tus objetivos.</p>
              <p>Pésate semanalmente y monitorea tendencias, no fluctuaciones diarias.</p>
            </div>

            <div class="step-card">
              <h4>Paso 4: Optimiza a Través de Evaluación</h4>
              <p>Reevalúa tus necesidades calóricas cada 4-6 semanas o después de cambios significativos de peso para mantener tu plan efectivo.</p>
            </div>
          </div>

          <div class="info-text">
            <h3>La Ciencia del Balance Calórico</h3>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li><strong>Fundamentos del Balance Energético:</strong> La gestión del peso se rige por calorías que entran versus calorías que salen.</li>
              <li><strong>Efecto Térmico de los Alimentos:</strong> Diferentes alimentos requieren cantidades variables de energía para digerir, impactando el total de calorías quemadas.</li>
              <li><strong>Saciedad y Calidad de los Alimentos:</strong> Alimentos altos en fibra y proteína promueven la saciedad, ayudándote a mantener tu plan.</li>
            </ul>

            <p style="font-style: italic; color: #374151; margin: 16px 0">
              Ejemplo: La 'Dieta Twinkie' mostró que la pérdida de peso es posible con un déficit calórico, pero las dietas pobres en nutrientes dañan la salud a largo plazo.
            </p>

            <h4 style="color: #416955; margin-top: 20px">Beneficios Adicionales del Seguimiento de Calorías</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Construye conciencia nutricional y hábitos de alimentación consciente.</li>
              <li>Mejora el control de porciones a través del seguimiento consistente.</li>
              <li>Conecta elecciones de alimentos con ejercicio para optimizar el balance energético.</li>
            </ul>
          </div>
        </section>

        <!-- Zigzag Calorie Cycling Section -->
        <section class="zigzag-section">
          <h2>Ciclado Zigzag de Calorías</h2>
          <p>El ciclado zigzag de calorías implica variar tu ingesta calórica diaria mientras mantienes tu objetivo semanal para mejorar la flexibilidad metabólica y prevenir mesetas.</p>

          <div class="zigzag-explanation">
            <div class="example-card">
              <h4>Ejemplo</h4>
              <p><strong>Objetivo Semanal:</strong> 14,000 calorías (2,000 calorías/día promedio)</p>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li><strong>Opción A:</strong> 7 días a 2,000 calorías.</li>
                <li><strong>Opción B:</strong> 5 días a 1,800 calorías, 2 días a 2,500 calorías.</li>
              </ul>
              <p>Ambas opciones cumplen el objetivo semanal pero varían la ingesta diaria para mantener tu metabolismo dinámico.</p>
            </div>

            <div class="benefits-card">
              <h4>Objetivo Zigzag</h4>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li>Mejor flexibilidad metabólica y adherencia.</li>
                <li>Más flexibilidad en la planificación de comidas, especialmente para eventos sociales.</li>
                <li>Previene la adaptación metabólica de déficits prolongados.</li>
                <li>Rompe las mesetas de pérdida de peso variando la ingesta calórica.</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Calorie Requirements Section -->
        <section class="requirements-section">
          <h2>Requisitos Calóricos por Estilo de Vida</h2>
          <p>Las necesidades calóricas varían por factores individuales, pero las pautas generales pueden ayudarte a comenzar.</p>

          <div class="factors-grid">
            <div class="factor-card">
              <h4>Factores Que Influyen en las Necesidades Calóricas</h4>
              <ul>
                <li>Edad, sexo, peso y altura.</li>
                <li>Nivel de actividad (sedentario a altamente activo).</li>
                <li>Estado de salud, incluyendo embarazo o condiciones médicas.</li>
              </ul>
            </div>

            <div class="guidelines-card">
              <h4>Pautas Generales</h4>
              <p><strong>Hombres:</strong> 2,000–3,000 calorías/día</p>
              <p><strong>Mujeres:</strong> 1,600–2,400 calorías/día</p>
            </div>

            <div class="minimum-card">
              <h4>Ingesta Mínima Segura</h4>
              <p><strong>Mujeres:</strong> 1,200 calorías/día</p>
              <p><strong>Hombres:</strong> 1,500 calorías/día</p>
              <p class="warning">Las ingestas por debajo de estos niveles deben ser supervisadas médicamente.</p>
            </div>
          </div>

          <div class="warning-note" style="margin-top: 20px">
            <p>Restringir excesivamente las calorías puede llevar a deficiencias nutricionales, pérdida muscular y ralentización metabólica. Siempre prioriza la salud.</p>
          </div>
        </section>

        <!-- Calorie Types Section -->
        <section class="types-section">
          <h2>No Todas las Calorías Son Iguales</h2>
          <p>Las calorías provienen de diferentes macronutrientes, cada uno con efectos únicos en tu cuerpo:</p>
          <ul style="list-style-type: disc; padding-left: 20px; margin: 16px 0">
            <li>Proteína: 4 calorías/gramo – apoya la reparación muscular y saciedad.</li>
            <li>Carbohidratos: 4 calorías/gramo – fuente primaria de energía.</li>
            <li>Grasa: 9 calorías/gramo – esencial para hormonas y absorción de nutrientes.</li>
            <li>Alcohol: 7 calorías/gramo – valor nutricional mínimo.</li>
          </ul>

          <p>Las etiquetas nutricionales proporcionan conteos calóricos precisos, pero los tamaños de porción y métodos de preparación importan.</p>

          <div class="calorie-types">
            <div class="type-card">
              <h4>Alimentos Altos en Calorías</h4>
              <p>Densos en calorías, a menudo debido a grasas o azúcares. Usa con moderación para gestión del peso.</p>
              <ul>
                <li>Aguacates, aceites.</li>
                <li>Nueces y semillas.</li>
                <li>Alimentos fritos.</li>
                <li>Postres y refrigerios azucarados.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Alimentos Bajos en Calorías</h4>
              <ul>
                <li>Muchas verduras (ej., espinaca, calabacín).</li>
                <li>Algunas frutas (ej., bayas).</li>
                <li>Proteínas magras (ej., pavo, pescado).</li>
                <li>Granos enteros con moderación.</li>
                <li>Verduras de hoja verde para volumen y nutrientes.</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Calorías Vacías</h4>
              <ul>
                <li>Bebidas azucaradas (ej., refrescos).</li>
                <li>Refrigerios procesados (ej., papas fritas, galletas).</li>
                <li>Azúcares añadidos en alimentos empaquetados.</li>
                <li>Grasas sólidas (ej., mantequilla, margarina).</li>
                <li>Alcohol con beneficio nutricional mínimo.</li>
              </ul>
            </div>
          </div>

          <div class="thermic-effect">
            <h3>Por Qué Importa la Calidad Calórica</h3>
            <p>Las bebidas como refrescos o alcohol añaden calorías sin saciedad, haciendo más difícil mantener un déficit.</p>

            <h4 style="color: #416955; margin-top: 16px">Construye un Plan Equilibrado</h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Enfócate en alimentos enteros y no procesados para densidad nutricional.</li>
              <li>Limita refrigerios y bebidas azucaradas.</li>
              <li>Usa alimentos altos en fibra y proteína para control natural de porciones.</li>
              <li>Combina el conteo de calorías con ejercicio para resultados sostenibles.</li>
            </ul>
          </div>
        </section>

        <!-- Final Takeaway Section -->
        <section class="info-section" style="margin-top: 40px">
          <h2>Conclusión Final</h2>
          <p>No hay un enfoque único para la nutrición. Usa esta calculadora como punto de partida, rastrea tu progreso y ajusta según sea necesario para cumplir tus necesidades únicas.</p>
          <p style="font-weight: 500; color: #416955">Rastrea sabiamente, come conscientemente y prioriza la salud a largo plazo.</p>
        </section>
      </main>
    </div>
    <script src="/es/calculators/calorie-calculator/index.js"></script>
    <script>
    const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
    const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
    const select = document.getElementById('language-select');
    const currentLang = window.location.pathname.split('/')[1] || 'en';
    locales.forEach(l => {
      const opt = document.createElement('option');
      opt.value = l;
      opt.textContent = languageNames[l] || l;
      if (l === currentLang) opt.selected = true;
      select.appendChild(opt);
    });
    select.addEventListener('change', function() {
      const newLang = this.value;
      let path = window.location.pathname.split('/');
      if (locales.includes(path[1])) { path[1] = newLang; }
      else { path = ['', newLang, ...path.slice(1)]; }
      localStorage.setItem('preferredLang', newLang);
      window.location.pathname = path.join('/');
    });
    // Persist language choice
    if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
      select.value = localStorage.getItem('preferredLang');
      select.dispatchEvent(new Event('change'));
    }
    </script>
  </body>
</html>