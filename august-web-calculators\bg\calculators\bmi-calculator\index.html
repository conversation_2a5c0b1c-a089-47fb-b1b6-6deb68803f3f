<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Калкулатор за BMI за възрастни - Прецизен BMI инструмент | MeetAugust</title>
    <meta
      name="description"
      content="Изчислете своя BMI с нашия усъвършенстван инструмент. Получете незабавни резултати и вижте в коя категория попада вашето тегло. Безплатно, точно и научно обосновано."
    />
    <meta
      name="keywords"
      content="bmi калкулатор, индекс на телесна маса, категория тегло, здравен калкулатор"
    />
    <meta name="author" content="MeetAugust" />
    <meta
      property="og:title"
      content="Калкулатор за BMI за възрастни - Прецизен BMI инструмент"
    />
    <meta
      property="og:description"
      content="Изчислете своя BMI и вижте в коя категория попада вашето тегло незабавно."
    />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Калкулатор за BMI за възрастни - Прецизен BMI инструмент"
    />
    <meta
      name="twitter:description"
      content="Изчислете своя BMI и вижте в коя категория попада вашето тегло незабавно."
    />
    <link rel="canonical" href="https://www.meetaugust.ai/bg/calculators/bmi-calculator" />
    <link rel="icon" href="/bg/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/bg/calculators/bmi-calculator/style.css">
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
        <div class="navbar">
          <div class="logo">
            <a href="/bg/calculators/">
                <img
              width="200"
              src="/bg/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Лого на калкулатора"
            />
            </a>
          </div>
          <div class="nav">
            <div class="language-switcher" id="language-switcher">
              <select id="language-select">
                <!-- Options will be populated by JS -->
              </select>
            </div>
            <a
              href="https://app.meetaugust.ai/join/app?utm=bmi_calculator_topnav"
              class="talk-to-august"
              >Говори с Август</a
            >
          </div>
        </div>
      </header>
        <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">Калкулатор за BMI за възрастни</h1>
          <div class="page-info">
            <div class="info-badge">
              <div class="shield-icon">✓</div>
              <span>За всички</span>
            </div>
          </div>
        </div>

        <div class="content-grid">
          <div class="info-section">
            <h2>Разбиране на BMI за възрастни: Какво трябва да знаете</h2>
            <p class="info-text">
              Калкулаторът за BMI за възрастни е надежден инструмент за оценка дали теглото ви е подходящо за вашия ръст. ...
            </p>

            <img
              src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BMI Scale"
              class="weight-image"
            />

            <div style="margin-top: 30px">
              <h3
                style="
                  color: var(--primary-color);
                  font-size: 20px;
                  margin-bottom: 15px;
                "
              >
                Безплатен калкулатор за BMI за възрастни (20+ години)
              </h3>
              <p style="color: var(--text-secondary); margin-bottom: 15px">
                Използвайте този калкулатор за BMI за възрастни, за да определите незабавно своя BMI и да видите в коя категория попада вашето тегло — поднормено, нормално, наднормено или затлъстяване. Този калкулатор е предназначен специално за възрастни над 20 години.
              </p>
              <!-- <p style="color: var(--text-secondary)">
                BMI е един от многото инструменти за оценка на общото здраве и потенциални рискове, свързани с теглото. Той трябва да се тълкува заедно с други клинични оценки като медицинска история, навици, физически преглед и лабораторни изследвания. -->
                <!-- <a href="#" style="color: var(--primary-color)">
                  Научете повече за Индекса на телесна маса
                </a>
                и как той се вписва във вашия здравен профил. Винаги се консултирайте с медицински специалист за персонализирани съвети — този инструмент е само за образователни цели и не замества медицински съвет.
              </p> -->
            </div>
          </div>

          <div class="calculator-section">
            <div style="display: flex; justify-content: space-between;" >

              <h3 class="calculator-title">Калкулатор за BMI</h3>
              <button class="download-btn  neon-pulse" style="margin-left: 2rem; margin-top: 0; display: none; align-self: flex-start; font-size: 1.15rem; padding: 1rem 2rem;" id="download-pdf-btn" onclick="downloadBMIResultsPDF()"><span style="font-size:1.5em;vertical-align:middle;">📄</span> Download BMI Results</button>
            </div>

            <div class="unit-selector">
              <div class="unit-option">
                <input
                  type="radio"
                  id="us-units"
                  name="units"
                  value="us"
                  checked
                />
                <label for="us-units">Американски единици</label>
              </div>
              <div class="unit-option">
                <input
                  type="radio"
                  id="metric-units"
                  name="units"
                  value="metric"
                />
                <label for="metric-units">Метрични единици</label>
              </div>
              <span class="reset-link" onclick="resetCalculator()">НУЛИРАЙ</span>
            </div>

            <div class="form-group">
              <label class="form-label">ВИСОЧИНА</label>
              <div class="input-group" id="height-inputs">
                <input
                  type="number"
                  class="form-input"
                  id="height-feet"
                  placeholder="0"
                  min="0"
                  max="10"
                />
                <span class="unit-label">фута (ft)</span>
                <input
                  type="number"
                  class="form-input"
                  id="height-inches"
                  placeholder="0"
                  min="0"
                  max="11"
                />
                <span class="unit-label">инча (in)</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">ТЕГЛО</label>
              <div class="input-group">
                <input
                  type="number"
                  class="form-input"
                  id="weight"
                  placeholder="0"
                  min="0"
                />
                <span class="unit-label" id="weight-unit">паунда (lbs)</span>
              </div>
            </div>

            <button class="calculate-btn" onclick="calculateBMI()">
              Изчисли
            </button>
          </div>
        </div>

        <div class="note-section">
          <p>
            <strong>Забележка:</strong> Този калкулатор за BMI изисква JavaScript, за да работи правилно. Ако вашият браузър има изключен JavaScript или имате проблеми, можете ръчно да изчислите своя BMI с тази формула:
            <em>BMI = (тегло в килограми) / (ръст в метри × ръст в метри)</em>
            Например, ако тежите 70 кг и сте високи 1,75 м, вашият BMI е 22,9.
          </p>
        </div>
      </main>

<!-- Result section moved below main content -->
<div class="result-section-wrapper">
  <div class="result-section" id="result-section">
    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
      <div>
        <div class="bmi-value" id="bmi-value">0.0</div>
        <div class="bmi-category" id="bmi-category"></div>
        <div class="bmi-description" id="bmi-description"></div>
      </div>
    </div>
  </div>
</div>

    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="/bg/calculators/bmi-calculator/script.js"></script>
    <script>
      const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
      const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
      const select = document.getElementById('language-select');
      const currentLang = window.location.pathname.split('/')[1] || 'en';
      locales.forEach(l => {
        const opt = document.createElement('option');
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener('change', function() {
        const newLang = this.value;
        let path = window.location.pathname.split('/');
        if (locales.includes(path[1])) { path[1] = newLang; }
        else { path = ['', newLang, ...path.slice(1)]; }
        localStorage.setItem('preferredLang', newLang);
        window.location.pathname = path.join('/');
      });
      // Persist language choice
      if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
        select.value = localStorage.getItem('preferredLang');
      }
    </script>
  </body>
</html> 