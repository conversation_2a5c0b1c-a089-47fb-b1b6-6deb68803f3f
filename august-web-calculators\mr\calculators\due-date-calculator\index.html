<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>मोफत गरोदरपण कॅल्क्युलेटर - प्रसूती तारीख आणि गर्भकालीन वय कॅल्क्युलेटर 2025</title>
    <meta name="description" content="आमच्या मोफत आणि अचूक गरोदरपण कॅल्क्युलेटरसह तुमची प्रसूती तारीख आणि गर्भकालीन वय मोजा. तुमच्या शेवटच्या मासिक पाळीच्या आधारावर तुमचा गरोदरपणाचा प्रवास आठवड्याने आठवडा ट्रॅक करा." />
    <meta name="keywords" content="गरोदरपण कॅल्क्युलेटर, प्रसूती तारीख कॅल्क्युलेटर, गर्भकालीन वय कॅल्क्युलेटर, गरोदरपण ट्रॅकर, गरोदरपण आठवडे, LMP कॅल्क्युलेटर, प्रसूती तारीख, भावी माता" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="मोफत गरोदरपण कॅल्क्युलेटर - प्रसूती तारीख आणि गर्भकालीन वय कॅल्क्युलेटर" />
    <meta property="og:description" content="आमच्या मोफत आणि अचूक गरोदरपण कॅल्क्युलेटरसह तुमची प्रसूती तारीख आणि गर्भकालीन वय मोजा. तुमचा गरोदरपणाचा प्रवास आठवड्याने आठवडा ट्रॅक करा." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/mr/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/mr/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/mr/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/mr/calculators/">
            <img
              width="200"
              src="/mr/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="कॅल्क्युलेटर लोगो"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >ऑगस्टशी बोला</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>प्रसूती तारीख कॅल्क्युलेटर</h1>
        <p>आपल्या शेवटच्या मासिक पाळीच्या किंवा गर्भधारणेच्या तारखेच्या आधारावर आपल्या बाळाची प्रसूती तारीख अंदाजा. आपल्या गरोदरपणाची योजना बनवण्यासाठी आणि बाळाच्या आगमनासाठी तयारी करण्यासाठी आमच्या प्रसूती तारीख कॅल्क्युलेटरचा वापर करा.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">जगभरातील भावी माता आणि आरोग्य व्यावसायिकांद्वारे विश्वासार्ह</span>
          </div>

          <h2>आमचे गरोदरपण कॅल्क्युलेटर का वापरावे?</h2>
          <p>आमचे मोफत गरोदरपण कॅल्क्युलेटर तुम्हाला तुमच्या शेवटच्या मासिक पाळीच्या (LMP) आधारावर तुमच्या बाळाची प्रसूती तारीख आणि सध्याचे गर्भकालीन वय निश्चित करण्यात मदत करते. तुम्ही नुकतीच गरोदर झाल्या असाल किंवा तुमच्या गरोदरपणाचा प्रवास ट्रॅक करत असाल, हे साधन तुमच्या बाळाच्या आगमनाची योजना करण्यासाठी अचूक गणना प्रदान करते.</p>

          <h3>मुख्य वैशिष्ट्ये:</h3>
          <ul>
            <li>LMP आधारित तात्काळ प्रसूती तारीख गणना</li>
            <li>सध्याचे गर्भकालीन वय आठवडे आणि दिवसांमध्ये</li>
            <li>ड्रॉपडाउन मेनूसह वापरण्यास सोपी इंटरफेस</li>
            <li>WHO मार्गदर्शक तत्त्वांचे पालन करणारी वैद्यकीयदृष्ट्या अचूक गणना</li>
            <li>नोंदणीशिवाय मोफत वापर</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">तुमची प्रसूती तारीख मोजा</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">आजची तारीख:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="सध्याची तारीख" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">शेवटच्या मासिक पाळीचा पहिला दिवस (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="LMP तारीख" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                आजवर सेट करा
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                निकाल साफ करा
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">तुमची अंदाजित प्रसूती तारीख:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  सध्याचे गर्भकालीन वय:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>गरोदरपण कॅल्क्युलेटरबद्दल वारंवार विचारले जाणारे प्रश्न</h2>

        <div class="faq-item">
          <div class="faq-question">हे गरोदरपण कॅल्क्युलेटर किती अचूक आहे?</div>
          <div class="faq-answer">आमचे गरोदरपण कॅल्क्युलेटर तुमच्या शेवटच्या मासिक पाळीला 280 दिवस जोडण्याची मानक वैद्यकीय सूत्र वापरते. ही पद्धत तुमच्या प्रसूती तारखेची अंदाजे 95% अचूकतेने भविष्यवाणी करते, जरी वैयक्तिक गरोदरपण दोन आठवड्यांपर्यंत बदलू शकते.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">जर मला माझ्या LMP ची नेमकी तारीख आठवत नसेल तर?</div>
          <div class="faq-answer">जर तुम्हाला तुमच्या LMP ची नेमकी तारीख आठवत नसेल, तर शक्य तितक्या जवळून अंदाज लावा. तुमचे आरोग्य सेवा प्रदाता तुमच्या पहिल्या प्रसवपूर्व भेटीदरम्यान अधिक अचूक प्रसूती तारीख प्रदान करण्यासाठी अल्ट्रासाऊंड मोजमापांचा वापर करू शकतात.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            जर माझ्या मासिक पाळ्या अनियमित असतील तर मी हे कॅल्क्युलेटर वापरू शकते का?
          </div>
          <div class="faq-answer">जर तुमच्या मासिक पाळ्या अनियमित असतील, तर हे कॅल्क्युलेटर कमी अचूक असू शकते. अशा परिस्थितीत, तुमचे डॉक्टर कदाचित तुमची प्रसूती तारीख अधिक अचूकपणे निर्धारित करण्यासाठी अल्ट्रासाऊंड डेटिंग वापरतील.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            मला माझी पहिली प्रसवपूर्व भेट कधी घ्यावी?
          </div>
          <div class="faq-answer">
            बहुतेक आरोग्य सेवा प्रदाते गरोदरपणाच्या 6 ते 8 आठवड्यांदरम्यान तुमची पहिली प्रसवपूर्व भेट घेण्याची शिफारस करतात. तुमचे सध्याचे गर्भकालीन वय निश्चित करण्यासाठी आमचे कॅल्क्युलेटर वापरा आणि त्यानुसार योजना करा.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            गर्भकालीन वय आणि भ्रूण वय यात काय फरक आहे?
          </div>
          <div class="faq-answer">गर्भकालीन वय तुमच्या शेवटच्या मासिक पाळीपासून मोजले जाते, तर भ्रूण वय गर्भधारणापासून मोजले जाते (सामान्यतः 2 आठवडे नंतर). वैद्यकीय व्यावसायिक सामान्यतः अधिक सातत्यासाठी गर्भकालीन वय वापरतात.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
