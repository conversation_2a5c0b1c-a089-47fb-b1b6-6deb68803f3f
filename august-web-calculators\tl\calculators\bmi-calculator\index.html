<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>BMI Calculator para sa Matanda – Tumpak na BMI Tool | MeetAugust</title>
    <meta
      name="description"
      content="Kalkulahin ang iyong BMI gamit ang aming advanced na tool. Makakuha ng agarang resulta at makita ang iyong kategorya ng timbang. Libre, tumpak, at batay sa agham."
    />
    <meta
      name="keywords"
      content="bmi calculator, body mass index, kategorya ng timbang, health calculator"
    />
    <meta name="author" content="MeetAugust" />
    <meta
      property="og:title"
      content="BMI Calculator para sa Matanda – Tumpak na BMI Tool"
    />
    <meta
      property="og:description"
      content="Kalkulahin ang iyong BMI at agad makita ang iyong kategorya ng timbang."
    />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="BMI Calculator para sa Matanda – Tumpak na BMI Tool"
    />
    <meta
      name="twitter:description"
      content="Kalkulahin ang iyong BMI at agad makita ang iyong kategorya ng timbang."
    />
    <link rel="canonical" href="https://www.meetaugust.ai/tl/calculators/bmi-calculator" />
    <link rel="icon" href="/tl/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/tl/calculators/bmi-calculator/style.css">
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
        <div class="navbar">
          <div class="logo">
            <a href="/tl/calculators/">
                <img
              width="200"
              src="/tl/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo ng Calculator"
            />
            </a>
          </div>
          <div class="nav">
            <div class="language-switcher" id="language-switcher">
              <select id="language-select">
                <!-- Options will be populated by JS -->
              </select>
            </div>
            <a
              href="https://app.meetaugust.ai/join/app?utm=bmi_calculator_topnav"
              class="talk-to-august"
              >Makipag-usap kay August</a
            >
          </div>
        </div>
      </header>
        <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">BMI Calculator para sa Matanda</h1>
          <div class="page-info">
            <div class="info-badge">
              <div class="shield-icon">✓</div>
              <span>Para sa lahat</span>
            </div>
          </div>
        </div>

        <div class="content-grid">
          <div class="info-section">
            <h2>Pag-unawa sa BMI para sa Matanda: Ano ang Dapat Mong Malaman</h2>
            <p class="info-text">
              Ang BMI Calculator para sa Matanda ay isang maaasahang tool upang matukoy kung ang iyong timbang ay angkop para sa iyong taas. ...
            </p>

            <img
              src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BMI Scale"
              class="weight-image"
            />

            <div style="margin-top: 30px">
              <h3
                style="
                  color: var(--primary-color);
                  font-size: 20px;
                  margin-bottom: 15px;
                "
              >
                Libreng BMI Calculator para sa Matanda (20+ taong gulang)
              </h3>
              <p style="color: var(--text-secondary); margin-bottom: 15px">
                Gamitin ang BMI calculator na ito para sa mga adult upang agad na matukoy ang iyong BMI at makita kung saang kategorya ng timbang ka nabibilang — kulang sa timbang, normal na timbang, sobrang timbang, o obese. Ang tool na ito ay partikular na para sa mga adult na higit sa 20 taong gulang.
              </p>
              <!-- <p style="color: var(--text-secondary)">
                Ang BMI ay isa lamang sa maraming tool para suriin ang pangkalahatang kalusugan at mga posibleng panganib na may kaugnayan sa timbang. Dapat itong bigyang-kahulugan kasama ng iba pang clinical assessments tulad ng medical history, lifestyle, physical exam, at laboratory tests. -->
                <!-- <a href="#" style="color: var(--primary-color)">
                  Alamin pa tungkol sa Body Mass Index
                </a>
                at kung paano ito umaangkop sa iyong health profile. Palaging kumonsulta sa isang health professional para sa personal na payo — ang tool na ito ay para lamang sa edukasyonal na layunin at hindi kapalit ng medikal na payo.
              </p> -->
            </div>
          </div>

          <div class="calculator-section">
            <div style="display: flex; justify-content: space-between;" >

              <h3 class="calculator-title">BMI Calculator</h3>
              <button class="download-btn  neon-pulse" style="margin-left: 2rem; margin-top: 0; display: none; align-self: flex-start; font-size: 1.15rem; padding: 1rem 2rem;" id="download-pdf-btn" onclick="downloadBMIResultsPDF()"><span style="font-size:1.5em;vertical-align:middle;">📄</span> Download BMI Results</button>
            </div>

            <div class="unit-selector">
              <div class="unit-option">
                <input
                  type="radio"
                  id="us-units"
                  name="units"
                  value="us"
                  checked
                />
                <label for="us-units">Mga Unit ng US</label>
              </div>
              <div class="unit-option">
                <input
                  type="radio"
                  id="metric-units"
                  name="units"
                  value="metric"
                />
                <label for="metric-units">Mga Metric Unit</label>
              </div>
              <span class="reset-link" onclick="resetCalculator()">I-RESET</span>
            </div>

            <div class="form-group">
              <label class="form-label">TAAS</label>
              <div class="input-group" id="height-inputs">
                <input
                  type="number"
                  class="form-input"
                  id="height-feet"
                  placeholder="0"
                  min="0"
                  max="10"
                />
                <span class="unit-label">talampakan (ft)</span>
                <input
                  type="number"
                  class="form-input"
                  id="height-inches"
                  placeholder="0"
                  min="0"
                  max="11"
                />
                <span class="unit-label">pulgada (in)</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">TIMBANG</label>
              <div class="input-group">
                <input
                  type="number"
                  class="form-input"
                  id="weight"
                  placeholder="0"
                  min="0"
                />
                <span class="unit-label" id="weight-unit">libra (lbs)</span>
              </div>
            </div>

            <button class="calculate-btn" onclick="calculateBMI()">
              Kalkulahin
            </button>
          </div>
        </div>

        <div class="note-section">
          <p>
            <strong>Tandaan:</strong> Ang BMI calculator na ito ay nangangailangan ng JavaScript para gumana nang tama. Kung naka-disable ang JavaScript sa iyong browser o may problema, maaari mong manu-manong kalkulahin ang iyong BMI gamit ang formula na ito:
            <em>BMI = (timbang sa kilo) / (taas sa metro × taas sa metro)</em>
            Halimbawa, kung ikaw ay may timbang na 70 kg at taas na 1.75 m, ang iyong BMI ay 22.9.
          </p>
        </div>
      </main>

<!-- Result section moved below main content -->
<div class="result-section-wrapper">
  <div class="result-section" id="result-section">
    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
      <div>
        <div class="bmi-value" id="bmi-value">0.0</div>
        <div class="bmi-category" id="bmi-category"></div>
        <div class="bmi-description" id="bmi-description"></div>
      </div>
    </div>
  </div>
</div>

    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="/tl/calculators/bmi-calculator/script.js"></script>
    <script>
      const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
      const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
      const select = document.getElementById('language-select');
      const currentLang = window.location.pathname.split('/')[1] || 'en';
      locales.forEach(l => {
        const opt = document.createElement('option');
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener('change', function() {
        const newLang = this.value;
        let path = window.location.pathname.split('/');
        if (locales.includes(path[1])) { path[1] = newLang; }
        else { path = ['', newLang, ...path.slice(1)]; }
        localStorage.setItem('preferredLang', newLang);
        window.location.pathname = path.join('/');
      });
      // Persist language choice
      if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
        select.value = localStorage.getItem('preferredLang');
      }
    </script>
  </body>
</html> 