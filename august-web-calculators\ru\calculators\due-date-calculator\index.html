<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Бесплатный калькулятор беременности - Калькулятор даты родов и гестационного возраста 2025</title>
    <meta name="description" content="Рассчитайте дату родов и гестационный возраст с помощью нашего бесплатного точного калькулятора беременности. Отслеживайте свою беременность неделя за неделей на основе последней менструации." />
    <meta name="keywords" content="калькулятор беременности, калькулятор даты родов, калькулятор гестационного возраста, трекер беременности, недели беременности, калькулятор ПМЦ, дата родов, будущие мамы" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Бесплатный калькулятор беременности - Калькулятор даты родов и гестационного возраста" />
    <meta property="og:description" content="Рассчитайте дату родов и гестационный возраст с помощью нашего бесплатного точного калькулятора беременности. Отслеживайте свою беременность неделя за неделей." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/ru/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/ru/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/ru/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/ru/calculators/">
            <img
              width="200"
              src="/ru/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Логотип Калькулятора"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Поговорить с Августом</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Калькулятор даты родов</h1>
        <p>Оцените дату родов вашего ребенка на основе последней менструации или даты зачатия. Используйте наш калькулятор даты родов для планирования беременности и подготовки к появлению ребенка.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Доверяют будущие мамы и медицинские работники по всему миру</span>
          </div>

          <h2>Почему стоит использовать наш калькулятор беременности?</h2>
          <p>Наш бесплатный калькулятор беременности помогает определить дату родов и текущий гестационный возраст на основе последней менструации (ПМЦ). Независимо от того, недавно ли вы забеременели или отслеживаете свою беременность, этот инструмент предоставляет точные расчеты, чтобы помочь вам подготовиться к рождению малыша.</p>

          <h3>Ключевые особенности:</h3>
          <ul>
            <li>Мгновенный расчет даты родов на основе ПМЦ</li>
            <li>Текущий гестационный возраст в неделях и днях</li>
            <li>Простой в использовании интерфейс с выпадающими меню</li>
            <li>Медицинско точные расчеты в соответствии с рекомендациями ВОЗ</li>
            <li>Бесплатно без регистрации</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Рассчитайте дату родов</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Сегодняшняя дата:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Текущая дата" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Первый день последней менструации (ПМЦ):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Дата ПМЦ" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Установить на сегодня
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Очистить результаты
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Ваша предполагаемая дата родов:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Текущий гестационный возраст:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Часто задаваемые вопросы о калькуляторах беременности</h2>

        <div class="faq-item">
          <div class="faq-question">Насколько точен этот калькулятор беременности?</div>
          <div class="faq-answer">Наш калькулятор беременности использует стандартную медицинскую формулу добавления 280 дней к последней менструации. Этот метод примерно на 95% точен для прогнозирования даты родов, хотя индивидуальные беременности могут варьироваться до двух недель.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Что если я не помню точную дату ПМЦ?</div>
          <div class="faq-answer">Если вы не можете вспомнить точную дату ПМЦ, попробуйте оценить как можно точнее. Ваш врач может использовать УЗИ для более точного определения даты родов во время первого пренатального визита.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Могу ли я использовать этот калькулятор при нерегулярных месячных?
          </div>
          <div class="faq-answer">При нерегулярном менструальном цикле этот калькулятор может быть менее точным. В таких случаях ваш врач, вероятно, будет использовать УЗИ для более точного определения даты родов.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Когда следует записаться на первый пренатальный прием?
          </div>
          <div class="faq-answer">
            Большинство врачей рекомендуют записываться на первый пренатальный прием между 6-8 неделями беременности. Используйте наш калькулятор для определения текущего гестационного возраста и планирования соответственно.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            В чем разница между гестационным и фетальным возрастом?
          </div>
          <div class="faq-answer">Гестационный возраст рассчитывается от последней менструации, а фетальный возраст рассчитывается от зачатия (обычно на 2 недели позже). Медицинские работники обычно используют гестационный возраст для согласованности.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
