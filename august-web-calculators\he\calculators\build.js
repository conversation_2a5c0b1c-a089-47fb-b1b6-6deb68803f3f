const fs = require('fs');
const path = require('path');

// Configuration
const languages = [
  'en', 'fr', 'de', 'es', 'it', 'sl', 'sk', 'mr', 'mk', 'bg', 'fi', 'he', 'el', 'ko', 'sr', 'lv', 'hr', 'ru', 'ja', 'tl', 'pt'
];
const baseUrl = 'https://www.meetaugust.ai';

// Language switcher URLs (circular navigation)
const languageSwitcherUrls = {
  'en': '/fr/calculators/',
  'fr': '/de/calculators/',
  'de': '/en/calculators/'
};

// Function to replace placeholders in template
function replacePlaceholders(template, locale) {
  let output = template;
  Object.entries(locale).forEach(([key, value]) => {
    // Replace both {{key}} and {key} placeholders
    const curlyRegex = new RegExp(`{{${key}}}`, 'g');
    const braceRegex = new RegExp(`{${key}}`, 'g');
    output = output.replace(curlyRegex, value);
    output = output.replace(braceRegex, value);
  });
  return output;
}

// Function to copy file
function copyFile(source, destination) {
  try {
    fs.copyFileSync(source, destination);
    console.log(`✓ Copied ${source} to ${destination}`);
  } catch (error) {
    console.error(`✗ Error copying ${source}:`, error.message);
  }
}

// Function to create directory if it doesn't exist
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✓ Created directory: ${dirPath}`);
  }
}

// Recursively walk through a directory and return all files (with their relative paths)
function walkDir(dir, baseDir = dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    if (stat && stat.isDirectory()) {
      // Skip locales directory
      if (file === 'locales') return;
      results = results.concat(walkDir(filePath, baseDir));
    } else {
      // Skip template files
      if (file.endsWith('-template.html')) return;
      results.push(path.relative(baseDir, filePath));
    }
  });
  return results;
}

// Main build function
function buildCalculators() {
  console.log('🚀 Starting calculator build process...\n');

  try {
    // Get all files in calculators/ (excluding locales and templates)
    const allFiles = walkDir('calculators');
    console.log('✓ Source files discovered');

    // Read homepage template once
    const homepageTemplate = fs.readFileSync('calculators/homepage-template.html', 'utf-8');

    // Process each language
    languages.forEach(lang => {
      console.log(`\n📝 Processing ${lang.toUpperCase()}...`);
      // Read locale file
      const localePath = `calculators/locales/${lang}.json`;
      const locale = JSON.parse(fs.readFileSync(localePath, 'utf-8'));
      // Update language switcher URL if present
      locale.language_switcher_url = languageSwitcherUrls[lang];

      allFiles.forEach(relFilePath => {
        // Source and destination paths
        const srcPath = path.join('calculators', relFilePath);
        let destPath = path.join('august-web-calculators', lang, 'calculators', relFilePath);
        const destDir = path.dirname(destPath);
        ensureDirectoryExists(destDir);

        // If this is calculators/index.html, use homepage-template.html and localize
        if (relFilePath === 'index.html') {
          const localized = replacePlaceholders(homepageTemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(`✓ Localized and generated homepage ${destPath}`);
        } else if (relFilePath === path.join("bmi-calculator", "index.html")) {
          // For bmi-calculator/index.html, use bmi-calculator-template.html and localize
          const bmiTemplate = fs.readFileSync(
            "calculators/bmi-calculator-template.html",
            "utf-8"
          );
          const localized = replacePlaceholders(bmiTemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(`✓ Localized and generated BMI calculator ${destPath}`);
        } else if (
          relFilePath === path.join("pregnancy-calculator", "index.html")
        ) {
          // For pregnancy-calculator/index.html, use pregnancy-caculator-template.html and localize
          const pregTemplate = fs.readFileSync(
            "calculators/pregnancy-caculator-template.html",
            "utf-8"
          );
          const localized = replacePlaceholders(pregTemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(
            `✓ Localized and generated Pregnancy calculator ${destPath}`
          );
        }else if (relFilePath === path.join("bra-size-calculator", "index.html")) {
          // For pregnancy-calculator/index.html, use pregnancy-caculator-template.html and localize
          const bratemplate = fs.readFileSync(
            "calculators/bra-size-template.html",
            "utf-8"
          );
          const localized = replacePlaceholders(bratemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(
            `✓ Localized and generated bra size calculator ${destPath}`
          );
        } else if (relFilePath === path.join("period-tracker", "index.html")) {
          // For period-tracker/index.html, use period-tracker-template.html and localize
          const ptTemplate = fs.readFileSync(
            "calculators/period-tracker-template.html",
            "utf-8"
          );
          const localized = replacePlaceholders(ptTemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(`✓ Localized and generated Period Tracker ${destPath}`);
        } else if (relFilePath === path.join("period-tracker", "index.html")) {
          // For period-tracker/index.html, use period-tracker-template.html and localize
          const ptTemplate = fs.readFileSync(
            "calculators/period-tracker-template.html",
            "utf-8"
          );
          const localized = replacePlaceholders(ptTemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(`✓ Localized and generated Period Tracker ${destPath}`);
        } else if (relFilePath === path.join("period-calculator", "index.html")) {
          // For period-tracker/index.html, use period-tracker-template.html and localize
          const ptTemplate = fs.readFileSync(
            "calculators/period-calculator-template.html",
            "utf-8"
          );
          const localized = replacePlaceholders(ptTemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(`✓ Localized and generated Period cal ${destPath}`);
        }else if (relFilePath === path.join("ovulation-tracker", "index.html")) {
          // For period-tracker/index.html, use period-tracker-template.html and localize
          const ptTemplate = fs.readFileSync(
            "calculators/ovulation-tracker-template.html",
            "utf-8"
          );
          const localized = replacePlaceholders(ptTemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(
            `✓ Localized and generated ovulation Tracker ${destPath}`
          );
        } else if (relFilePath === path.join("due-date-calculator", "index.html")) {
          // For period-tracker/index.html, use period-tracker-template.html and localize
          const ptTemplate = fs.readFileSync(
            "calculators/due-date-calculator-template.html",
            "utf-8"
          );
          const localized = replacePlaceholders(ptTemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(`✓ Localized and generated due-date calculator ${destPath}`);
        } else if (
          relFilePath === path.join("calorie-calculator", "index.html")
        ) {
          // For calorie-calculator/index.html, use calorie-calculator-template.html and localize
          const calorieTemplate = fs.readFileSync(
            "calculators/calorie-calculator-template.html",
            "utf-8"
          );
          const localized = replacePlaceholders(calorieTemplate, locale);
          fs.writeFileSync(destPath, localized);
          console.log(
            `✓ Localized and generated Calorie calculator ${destPath}`
          );
        } else if (relFilePath.endsWith(".html")) {
          // Localize other HTML files
          const template = fs.readFileSync(srcPath, "utf-8");
          const localized = replacePlaceholders(template, locale);
          fs.writeFileSync(destPath, localized);
          console.log(`✓ Localized and generated ${destPath}`);
        } else {
          // Copy JS, CSS, assets, etc. as-is
          copyFile(srcPath, destPath);
        }
      });
    });

    console.log('\n🎉 Build completed successfully!');
    console.log('\n📁 Generated structure:');
    languages.forEach(lang => {
      console.log(`  /${lang}/calculators/ (mirrors calculators/ structure)`);
    });
    console.log('\n🌐 Language switcher navigation:');
    console.log('  EN → FR → DE → EN (circular)');
  } catch (error) {
    console.error('\n❌ Build failed:', error.message);
    process.exit(1);
  }
}

// Run the build
buildCalculators(); 