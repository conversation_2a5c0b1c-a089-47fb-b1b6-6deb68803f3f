<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Libreng Calculator ng Pagbubuntis - Calculator ng Due Date at Gestational Age 2025</title>
    <meta name="description" content="Kalkulahin ang inyong due date at gestational age gamit ang aming libreng, tumpak na calculator ng pagbubuntis. Subaybayan ang inyong pagbubuntis linggo-linggo batay sa huling regla." />
    <meta name="keywords" content="calculator ng pagbubuntis, calculator ng due date, calculator ng gestational age, tracker ng pagbubuntis, mga linggo ng pagbubuntis, LMP calculator, due date ng pagbubuntis, mga inaasahang ina" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Libreng Calculator ng Pagbubuntis - Calculator ng Due Date at Gestational Age" />
    <meta property="og:description" content="Kalkulahin ang inyong due date at gestational age gamit ang aming libreng, tumpak na calculator ng pagbubuntis. Subaybayan ang inyong pagbubuntis linggo-linggo." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/tl/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/tl/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/tl/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/tl/calculators/">
            <img
              width="200"
              src="/tl/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo ng Calculator"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Makipag-usap kay August</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Libreng Calculator ng Pagbubuntis at Calculator ng Due Date</h1>
        <p>Kalkulahin ang inyong due date at subaybayan ang gestational age gamit ang aming tumpak, madaling gamitin na calculator ng pagbubuntis</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Pinagkakatiwalaan ng mga inaasahang ina at mga propesyonal sa kalusugan sa buong mundo</span>
          </div>

          <h2>Bakit Gamitin ang Aming Calculator ng Pagbubuntis?</h2>
          <p>Ang aming libreng calculator ng pagbubuntis ay tumutulong sa inyo na matukoy ang due date ng inyong sanggol at kasalukuyang gestational age batay sa inyong huling regla (LMP). Maging bagong buntis kayo o sinusubaybayan ang inyong pagbubuntis, ang tool na ito ay nagbibigay ng tumpak na mga kalkulasyon upang makatulong sa inyo na maghanda para sa pagdating ng inyong sanggol.</p>

          <h3>Mga Pangunahing Tampok:</h3>
          <ul>
            <li>Agarang kalkulasyon ng due date batay sa LMP</li>
            <li>Kasalukuyang gestational age sa mga linggo at araw</li>
            <li>Madaling gamitin na interface na may dropdown menu</li>
            <li>Medikal na tumpak na mga kalkulasyon na sumusunod sa WHO guidelines</li>
            <li>Libre gamitin nang walang registration</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Kalkulahin ang Inyong Due Date</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Petsa Ngayon:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Kasalukuyang petsa" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Unang Araw ng Huling Regla (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="LMP petsa" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Itakda sa Ngayon
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Burahin ang mga Resulta
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Inyong Tinatantiyang Due Date:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Kasalukuyang Gestational Age:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Mga Madalas Itanong Tungkol sa mga Calculator ng Pagbubuntis</h2>

        <div class="faq-item">
          <div class="faq-question">Gaano ka-tumpak ang calculator ng pagbubuntis na ito?</div>
          <div class="faq-answer">Ang aming calculator ng pagbubuntis ay gumagamit ng standard na medikal na formula na nagdadagdag ng 280 araw sa inyong huling regla. Ang pamamaraang ito ay humigit-kumulang 95% tumpak para sa pagtantiya ng inyong due date, bagama't ang mga indibidwal na pagbubuntis ay maaaring mag-vary hanggang dalawang linggo.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Paano kung hindi ko matandaan ang eksaktong LMP date?</div>
          <div class="faq-answer">Kung hindi ninyo matandaan ang eksaktong LMP date, subukan ninyong tantiyahin nang malapit hanggang sa makakaya. Ang inyong healthcare provider ay maaaring gumamit ng ultrasound measurements upang magbigay ng mas tumpak na due date sa inyong unang prenatal visit.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Maaari ko bang gamitin ang calculator na ito kung may irregular period ako?
          </div>
          <div class="faq-answer">Kung may irregular menstrual cycles kayo, ang calculator na ito ay maaaring hindi gaanong tumpak. Sa mga ganitong kaso, ang inyong doktor ay malamang na gagamit ng ultrasound dating upang mas tumpak na matukoy ang inyong due date.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Kailan ko dapat i-schedule ang aking unang prenatal appointment?
          </div>
          <div class="faq-answer">
            Karamihan sa mga healthcare provider ay nagre-recommend na i-schedule ang unang prenatal appointment sa pagitan ng 6-8 linggo ng pagbubuntis. Gamitin ang aming calculator upang matukoy ang inyong kasalukuyang gestational age at magplano nang naaayon.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Ano ang pagkakaiba ng gestational age at fetal age?
          </div>
          <div class="faq-answer">Ang gestational age ay kinakalkula mula sa inyong huling regla, habang ang fetal age ay kinakalkula mula sa conception (karaniwang 2 linggo mamaya). Ang mga medikal na propesyonal ay karaniwang gumagamit ng gestational age para sa pagkakapare-pareho.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
