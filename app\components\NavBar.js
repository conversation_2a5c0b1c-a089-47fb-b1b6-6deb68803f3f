'use client';
import { useState } from 'react';
// Optimized MUI imports for better tree shaking
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MenuIcon from '@mui/icons-material/Menu';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from '../contexts/LanguageContext';
import LanguageSwitcher from './LanguageSwitcher';
import { getRedirectPath } from '@/app/utils/getRedirectPath';
import blogNavigation from '@/app/contexts/BlogNavigation';

export default function NavBar() {
  const { t, language } = useLanguage();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [anchorEl, setAnchorEl] = useState(null);
  const blogMessage = blogNavigation[language] || blogNavigation.en;
  
  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <AppBar position="sticky" sx={{ backgroundColor: "#FFF", color: "#111" }}>
      <Toolbar
        sx={{
          minHeight: { xs: "56px", sm: "64px" },
          px: { xs: 1, sm: 2 },
        }}
      >
        {/* Logo */}
        <Box
          sx={{ display: "flex", alignItems: "center", flexGrow: 0, mr: 1 }}
          component={Link}
          href={"https://www.meetaugust.ai/"}
          style={{ textDecoration: "none", color: "inherit" }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              maxWidth: { xs: "120px", sm: "130px" },
            }}
          >
            <Image
              src="https://res.cloudinary.com/dpgnd3ad7/image/upload/v1738557729/august_logo_green_nd4fn9.svg"
              alt="Health Library Logo"
              width={60}
              height={12}
              style={{
                width: "auto",
                height: "auto",
                maxWidth: { xs: "80px", sm: "60px" },
              }}
              priority
            />
            <Typography
              sx={{
                whiteSpace: "nowrap",
                margin: 0,
                paddingLeft: "8px",
                fontSize: { xs: "16px", sm: "18px" },
                borderLeft: "1px solid #111",
                display: { xs: "none", sm: "block" },
              }}
            >
              Health Library
            </Typography>
          </Box>
        </Box>

        {/* Navigation Links - Desktop */}
        <Box
          sx={{
            display: { xs: "none", sm: "flex" },
            gap: 0,
            ml: "auto",

            justifyContent: "flex-end",
            pl: "60px",
            alignItems: "center",
            flexWrap: "wrap",
            "& .MuiButton-root": {
              whiteSpace: "nowrap",
              minWidth: "auto",
              fontSize: "75%",
              "@media (min-width:1491px)": {
                fontSize: "95%",
              },
              px: 1,
            },
          }}
        >
          <Link
            href={getRedirectPath(`/${language}/medications`)}
            passHref
            style={{ textDecoration: "none" }}
          >
            <Button color="inherit">{t("navigation.medications")}</Button>
          </Link>

          <Link
            href={getRedirectPath(`/${language}/tests-procedures`)}
            passHref
            style={{ textDecoration: "none" }}
          >
            <Button color="inherit">{t("navigation.tests")}</Button>
          </Link>

          <Link
            href={getRedirectPath(`/${language}/diseases-conditions`)}
            passHref
            style={{ textDecoration: "none" }}
          >
            <Button color="inherit">{t("navigation.diseases")}</Button>
          </Link>

          <Link
            href={getRedirectPath(`/${language}/symptoms`)}
            passHref
            style={{ textDecoration: "none" }}
          >
            <Button color="inherit">{t("navigation.symptoms")}</Button>
          </Link>
          <Link
            href={getRedirectPath(`/${language}/blog`)}
            passHref
            style={{ textDecoration: "none" }}
          >
            <Button color="inherit">{blogMessage}</Button>
          </Link>
        </Box>

        {/* Mobile Layout - Talk to August (center), Language Switcher (middle), Menu Icon (right) */}
        <Box
          sx={{
            ml: "auto",
            display: { xs: "flex", sm: "none" },
            alignItems: "center",
            gap: 2,
          }}
        >
          {/* Talk to August Button - Center */}

          <Link
            href="https://app.meetaugust.ai/join/app?utm=health_lib_topnav"
            className="no-underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            <button
              className="
     bg-[#416955] 
      text-white 
      font-semibold 
      w-[100%]
mr-[3rem]      text-[14px] sm:text-base
      normal-case 
      rounded-lg 
       sm:px-[11ps] 
      py-[8px] sm:py-3
      ml-2
      shadow-sm
      hover:bg-[#365747] 
      hover:shadow-md
      transition-all 
      duration-200
      flex
      justify-center
      gap-2
    "
            >
              Get it on
              <svg
                width="20"
                height="24"
                viewBox="0 0 20 24"
                fill="none"
                className="w-5 h-5 mb-[5px]"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M16.7045 12.7631C16.7166 11.8432 16.9669 10.9413 17.4321 10.1412C17.8972 9.34108 18.5621 8.66885 19.3648 8.18702C18.8548 7.47597 18.1821 6.89081 17.4 6.478C16.6178 6.0652 15.7479 5.83613 14.8592 5.80898C12.9635 5.61471 11.1258 6.91644 10.1598 6.91644C9.17506 6.91644 7.68776 5.82827 6.08616 5.86044C5.05021 5.89311 4.04059 6.18722 3.15568 6.7141C2.27077 7.24099 1.54075 7.98268 1.03674 8.86691C-1.14648 12.5573 0.482005 17.9809 2.57338 20.964C3.61975 22.4247 4.84264 24.0564 6.44279 23.9985C8.00863 23.9351 8.59344 23.0237 10.4835 23.0237C12.3561 23.0237 12.9048 23.9985 14.5374 23.9617C16.2176 23.9351 17.2762 22.4945 18.2859 21.02C19.0377 19.9792 19.6162 18.8288 20 17.6116C19.0238 17.2085 18.1908 16.5338 17.6048 15.6716C17.0187 14.8094 16.7056 13.7979 16.7045 12.7631Z"
                  fill="white"
                />
                <path
                  d="M13.6208 3.84713C14.5369 2.77343 14.9883 1.39335 14.879 0C13.4794 0.143519 12.1865 0.796596 11.258 1.82911C10.804 2.33351 10.4563 2.92033 10.2348 3.55601C10.0132 4.19168 9.92221 4.86375 9.96687 5.5338C10.6669 5.54084 11.3595 5.3927 11.9924 5.10054C12.6254 4.80838 13.1821 4.37982 13.6208 3.84713Z"
                  fill="white"
                />
              </svg>
              <svg
                width="21"
                height="24"
                className="w-5 h-5  mb-[4px] "
                viewBox="0 0 21 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.80482 11.4617L0.0896006 22.0059C0.090513 22.0078 0.0905129 22.0106 0.0914254 22.0125C0.389808 23.1574 1.41179 24 2.62539 24C3.11084 24 3.56616 23.8656 3.95671 23.6305L3.98773 23.6118L14.9229 17.1593L9.80482 11.4617Z"
                  fill="#EA4335"
                />
                <path
                  d="M19.6331 9.66619L19.624 9.65966L14.9028 6.86123L9.58392 11.7013L14.9219 17.1582L19.6176 14.3878C20.4406 13.9324 21 13.045 21 12.0223C21 11.0052 20.4489 10.1225 19.6331 9.66619Z"
                  fill="#FBBC04"
                />
                <path
                  d="M0.0894234 1.99332C0.0310244 2.21353 0 2.44495 0 2.68382V21.3164C0 21.5552 0.0310245 21.7866 0.0903359 22.0059L10.1386 11.7313L0.0894234 1.99332Z"
                  fill="#4285F4"
                />
                <path
                  d="M9.87657 11.9999L14.9044 6.85936L3.98192 0.383512C3.58499 0.139967 3.12145 0 2.62597 0C1.41237 0 0.38856 0.844473 0.0901778 1.99034C0.0901778 1.99128 0.0892658 1.99221 0.0892658 1.99314L9.87657 11.9999Z"
                  fill="#34A853"
                />
              </svg>
            </button>
          </Link>

          {/* Language Switcher - Middle */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
            }}
          >
            <LanguageSwitcher />
          </Box>

          {/* Menu Icon - Right */}
          <IconButton
            size="large"
            edge="start"
            color="inherit"
            aria-label="menu"
            onClick={handleMenu}
          >
            <MenuIcon />
          </IconButton>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            keepMounted
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            PaperProps={{
              elevation: 3,
              sx: {
                mt: 1.5,
                width: "200px",
                maxWidth: "90vw",
                borderRadius: 1,
                "& .MuiMenuItem-root": {
                  px: 2,
                  py: 1.5,
                  typography: "body1",
                  borderBottom: "1px solid",
                  borderColor: "divider",
                  "&:last-child": {
                    borderBottom: "none",
                  },
                  wordBreak: "break-word",
                  whiteSpace: "normal",
                },
              },
            }}
          >
            <MenuItem
              onClick={handleClose}
              component={Link}
              href={getRedirectPath(`/${language}/medications`)}
            >
              {t("navigation.medications")}
            </MenuItem>
            <MenuItem
              onClick={handleClose}
              component={Link}
              href={getRedirectPath(`/${language}/tests-procedures`)}
            >
              {t("navigation.tests")}
            </MenuItem>
            <MenuItem
              onClick={handleClose}
              component={Link}
              href={getRedirectPath(`/${language}/diseases-conditions`)}
            >
              {t("navigation.diseases")}
            </MenuItem>
            <MenuItem
              onClick={handleClose}
              component={Link}
              href={getRedirectPath(`/${language}/symptoms`)}
            >
              {t("navigation.symptoms")}
            </MenuItem>
            <MenuItem
              onClick={handleClose}
              component={Link}
              href={getRedirectPath(`/${language}/blog`)}
            >
              {t("navigation.featured_articles")}
            </MenuItem>
          </Menu>
        </Box>

        {/* Language Switcher - Desktop Only */}
        <Box
          sx={{
            ml: { xs: 0, sm: 2 },
            position: "relative",
            display: { xs: "none", sm: "flex" },
            alignItems: "center",
            height: "100%",
          }}
        >
          <LanguageSwitcher />
          <Link
            href="https://app.meetaugust.ai/join/app?utm=health_lib_topnav"
            className="no-underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            <button
              className="
      bg-[#416955]
      text-white
      font-semibold
      text-base
      normal-case
      rounded-lg
      px-5
      py-2.5
      flex gap-2
      items-center
      ml-2
      shadow-md
      hover:bg-[#365747]
      hover:shadow-lg
      transition-all
      duration-200
    "
            >
              Download app on
              <svg
                width="20"
                height="24"
                viewBox="0 0 20 24"
                fill="none"
                className="w-5 h-5 mb-[5px]"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M16.7045 12.7631C16.7166 11.8432 16.9669 10.9413 17.4321 10.1412C17.8972 9.34108 18.5621 8.66885 19.3648 8.18702C18.8548 7.47597 18.1821 6.89081 17.4 6.478C16.6178 6.0652 15.7479 5.83613 14.8592 5.80898C12.9635 5.61471 11.1258 6.91644 10.1598 6.91644C9.17506 6.91644 7.68776 5.82827 6.08616 5.86044C5.05021 5.89311 4.04059 6.18722 3.15568 6.7141C2.27077 7.24099 1.54075 7.98268 1.03674 8.86691C-1.14648 12.5573 0.482005 17.9809 2.57338 20.964C3.61975 22.4247 4.84264 24.0564 6.44279 23.9985C8.00863 23.9351 8.59344 23.0237 10.4835 23.0237C12.3561 23.0237 12.9048 23.9985 14.5374 23.9617C16.2176 23.9351 17.2762 22.4945 18.2859 21.02C19.0377 19.9792 19.6162 18.8288 20 17.6116C19.0238 17.2085 18.1908 16.5338 17.6048 15.6716C17.0187 14.8094 16.7056 13.7979 16.7045 12.7631Z"
                  fill="white"
                />
                <path
                  d="M13.6208 3.84713C14.5369 2.77343 14.9883 1.39335 14.879 0C13.4794 0.143519 12.1865 0.796596 11.258 1.82911C10.804 2.33351 10.4563 2.92033 10.2348 3.55601C10.0132 4.19168 9.92221 4.86375 9.96687 5.5338C10.6669 5.54084 11.3595 5.3927 11.9924 5.10054C12.6254 4.80838 13.1821 4.37982 13.6208 3.84713Z"
                  fill="white"
                />
              </svg>
              <svg
                width="21"
                height="24"
                className="w-5 h-5  mb-[4px] "
                viewBox="0 0 21 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.80482 11.4617L0.0896006 22.0059C0.090513 22.0078 0.0905129 22.0106 0.0914254 22.0125C0.389808 23.1574 1.41179 24 2.62539 24C3.11084 24 3.56616 23.8656 3.95671 23.6305L3.98773 23.6118L14.9229 17.1593L9.80482 11.4617Z"
                  fill="#EA4335"
                />
                <path
                  d="M19.6331 9.66619L19.624 9.65966L14.9028 6.86123L9.58392 11.7013L14.9219 17.1582L19.6176 14.3878C20.4406 13.9324 21 13.045 21 12.0223C21 11.0052 20.4489 10.1225 19.6331 9.66619Z"
                  fill="#FBBC04"
                />
                <path
                  d="M0.0894234 1.99332C0.0310244 2.21353 0 2.44495 0 2.68382V21.3164C0 21.5552 0.0310245 21.7866 0.0903359 22.0059L10.1386 11.7313L0.0894234 1.99332Z"
                  fill="#4285F4"
                />
                <path
                  d="M9.87657 11.9999L14.9044 6.85936L3.98192 0.383512C3.58499 0.139967 3.12145 0 2.62597 0C1.41237 0 0.38856 0.844473 0.0901778 1.99034C0.0901778 1.99128 0.0892658 1.99221 0.0892658 1.99314L9.87657 11.9999Z"
                  fill="#34A853"
                />
              </svg>
            </button>
          </Link>
        </Box>
      </Toolbar>
    </AppBar>
  );
}