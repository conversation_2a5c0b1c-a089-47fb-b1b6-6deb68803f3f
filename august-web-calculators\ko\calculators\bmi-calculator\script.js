let currentUnit = "us";

// Unit conversion listeners
document.querySelectorAll('input[name="units"]').forEach((radio) => {
  radio.addEventListener("change", function () {
    currentUnit = this.value;
    updateUnits();
    clearResults();
  });
});

function updateUnits() {
  const heightInputs = document.getElementById("height-inputs");
  const weightUnit = document.getElementById("weight-unit");

  if (currentUnit === "metric") {
    heightInputs.innerHTML = `
      <input type="number" class="form-input" id="height-cm" placeholder="0" min="0" max="300">
      <span class="unit-label">centimeters (cm)</span>
    `;
    weightUnit.textContent = "kilograms (kg)";
  } else {
    heightInputs.innerHTML = `
      <input type="number" class="form-input" id="height-feet" placeholder="0" min="0" max="10">
      <span class="unit-label">feet (ft)</span>
      <input type="number" class="form-input" id="height-inches" placeholder="0" min="0" max="11">
      <span class="unit-label">inches (in)</span>
    `;
    weightUnit.textContent = "pounds (lbs)";
  }
}

function resetCalculator() {
  document.getElementById("weight").value = "";
  if (currentUnit === "us") {
    document.getElementById("height-feet").value = "";
    document.getElementById("height-inches").value = "";
  } else {
    document.getElementById("height-cm").value = "";
  }
  clearResults();
  // Calculator section remains visible, just hide result
  const resultSection = document.getElementById("result-section");
  if (resultSection) resultSection.style.display = "none";
}

function clearResults() {
  const resultSection = document.getElementById("result-section");
  if (resultSection) resultSection.classList.remove("show");
  // Remove full width when results are cleared
  const infoSection = document.querySelector(".info-section");
  if (infoSection) infoSection.classList.remove("full-width-info");
}

function calculateBMI() {
  let heightInMeters, weightInKg;

  // Get weight
  const weight = parseFloat(document.getElementById("weight").value);
  if (!weight || weight <= 0) {
    alert("Please enter a valid weight.");
    return;
  }

  if (currentUnit === "us") {
    // Get height in feet and inches
    const feet = parseFloat(document.getElementById("height-feet").value) || 0;
    const inches =
      parseFloat(document.getElementById("height-inches").value) || 0;

    if (feet === 0 && inches === 0) {
      alert("Please enter a valid height.");
      return;
    }

    // Convert to metric
    const totalInches = feet * 12 + inches;
    heightInMeters = totalInches * 0.0254;
    weightInKg = weight * 0.453592;
  } else {
    // Metric units
    const heightCm = parseFloat(document.getElementById("height-cm").value);
    if (!heightCm || heightCm <= 0) {
      alert("Please enter a valid height.");
      return;
    }

    heightInMeters = heightCm / 100;
    weightInKg = weight;
  }

  // Calculate BMI
  const bmi = weightInKg / (heightInMeters * heightInMeters);

  // Display results
  displayResults(bmi);
  // Show result section
  const resultSection = document.getElementById("result-section");
  if (resultSection) resultSection.style.display = "block";
  // Scroll to result section
  if (resultSection) resultSection.scrollIntoView({ behavior: "smooth", block: "start" });
}

// --- BMI Gauge Helper (Animated) ---
function renderBMIGauge(bmi, pointerAngle = null) {
  // Gauge parameters
  const minBMI = 15;
  const maxBMI = 40;
  const gaugeStart = -120; // degrees
  const gaugeEnd = 120; // degrees
  const gaugeRange = gaugeEnd - gaugeStart;
  // Clamp BMI for pointer
  const clampedBMI = Math.max(minBMI, Math.min(maxBMI, bmi));
  // Calculate angle for pointer
  const angle = pointerAngle !== null
    ? pointerAngle
    : gaugeStart + ((clampedBMI - minBMI) / (maxBMI - minBMI)) * gaugeRange;

  // Gauge segments
  const segments = [
    { label: 'Underweight', color: '#e57373', from: 15, to: 18.5 },
    { label: 'Normal', color: '#4caf50', from: 18.5, to: 25 },
    { label: 'Overweight', color: '#ffd600', from: 25, to: 30 },
    { label: 'Obesity', color: '#ef4444', from: 30, to: 40 },
  ];

  // SVG arc generator for each segment
  function describeArc(cx, cy, r, startAngle, endAngle) {
    const start = polarToCartesian(cx, cy, r, endAngle);
    const end = polarToCartesian(cx, cy, r, startAngle);
    const arcSweep = endAngle - startAngle <= 180 ? "0" : "1";
    return [
      "M", start.x, start.y,
      "A", r, r, 0, arcSweep, 0, end.x, end.y
    ].join(" ");
  }
  function polarToCartesian(cx, cy, r, angleDeg) {
    const angleRad = (angleDeg - 90) * Math.PI / 180.0;
    return {
      x: cx + r * Math.cos(angleRad),
      y: cy + r * Math.sin(angleRad)
    };
  }

  // SVG gauge (smaller size)
  const cx = 80, cy = 80, r = 65;
  let currentStart = gaugeStart;
  let arcs = '';
  segments.forEach(seg => {
    const segStart = currentStart;
    const segEnd = gaugeStart + ((seg.to - minBMI) / (maxBMI - minBMI)) * gaugeRange;
    arcs += `<path d='${describeArc(cx, cy, r, segStart, segEnd)}' stroke='${seg.color}' stroke-width='12' fill='none' />`;
    // Add label
    const labelAngle = (segStart + segEnd) / 2;
    const labelPos = polarToCartesian(cx, cy, r + 18, labelAngle);
    // arcs += `<text x='${labelPos.x}' y='${labelPos.y}' text-anchor='middle' alignment-baseline='middle' font-size='10' fill='#333'>${seg.label}</text>`;
    currentStart = segEnd;
  });
  // Pointer
  const pointerLen = r - 18;
  const pointerEnd = polarToCartesian(cx, cy, pointerLen, angle);
  const pointer = `<line x1='${cx}' y1='${cy}' x2='${pointerEnd.x}' y2='${pointerEnd.y}' stroke='#222' stroke-width='3' marker-end='url(#arrowhead)' />`;
  // Pointer circle
  const pointerCircle = `<circle cx='${cx}' cy='${cy}' r='6' fill='#222' />`;
  // BMI value in center
  const bmiText = `<text x='${cx}' y='${cy + 28}' text-anchor='middle' font-size='16' font-weight='bold' fill='#206e55'>BMI = ${bmi.toFixed(1)}</text>`;
  // SVG
  return `
    <svg width='160' height='110' viewBox='0 0 160 110'>
      <defs>
        <marker id='arrowhead' markerWidth='8' markerHeight='8' refX='4' refY='4' orient='auto' markerUnits='strokeWidth'>
          <path d='M0,0 L8,4 L0,8 L2,4 Z' fill='#222' />
        </marker>
      </defs>
      ${arcs}
      ${pointer}
      ${pointerCircle}
      ${bmiText}
    </svg>
    
  `;
}

// Animate the gauge pointer
function animateBMIGauge(bmi) {
  const minBMI = 15;
  const maxBMI = 40;
  const gaugeStart = -120;
  const gaugeEnd = 120;
  const gaugeRange = gaugeEnd - gaugeStart;
  const clampedBMI = Math.max(minBMI, Math.min(maxBMI, bmi));
  const targetAngle = gaugeStart + ((clampedBMI - minBMI) / (maxBMI - minBMI)) * gaugeRange;
  let currentAngle = gaugeStart;
  const step = (targetAngle - gaugeStart) / 40; // 40 frames for smoothness
  const gaugeContainer = document.querySelector('.bmi-gauge-container');
  if (!gaugeContainer) return;

  function animate() {
    if ((step > 0 && currentAngle < targetAngle) || (step < 0 && currentAngle > targetAngle)) {
      currentAngle += step;
      gaugeContainer.innerHTML = renderBMIGauge(bmi, currentAngle);
      requestAnimationFrame(animate);
    } else {
      gaugeContainer.innerHTML = renderBMIGauge(bmi, targetAngle);
    }
  }
  animate();
}

function displayResults(bmi) {
  const resultSection = document.getElementById("result-section");
  if (!resultSection) return;

  let category, description, color;

  if (bmi < 18.5) {
    category = "Underweight";
    description =
      "Based on the information you entered, your body mass index (BMI) is " +
      bmi.toFixed(1) +
      ", indicating your weight is in the Underweight category for adults of your height.";
    color = "#6b7280";
  } else if (bmi >= 18.5 && bmi < 25) {
    category = "Healthy Weight";
    description =
      "Based on the information you entered, your body mass index (BMI) is " +
      bmi.toFixed(1) +
      ", indicating your weight is in the Healthy Weight category for adults of your height.";
    color = "#416955";
  } else if (bmi >= 25 && bmi < 30) {
    category = "Overweight";
    description =
      "Based on the information you entered, your body mass index (BMI) is " +
      bmi.toFixed(1) +
      ", indicating your weight is in the Overweight category for adults of your height.";
    color = "#f59e0b";
  } else {
    category = "Obesity";
    description =
      "Based on the information you entered, your body mass index (BMI) is " +
      bmi.toFixed(1) +
      ", indicating your weight is in the Obesity category for adults of your height.";
    color = "#ef4444";
  }

  // Get height and weight values for display
  let heightDisplay, weightDisplay;

  if (currentUnit === "us") {
    const feet = parseFloat(document.getElementById("height-feet").value) || 0;
    const inches =
      parseFloat(document.getElementById("height-inches").value) || 0;
    const weight = parseFloat(document.getElementById("weight").value);

    heightDisplay = `${feet}${feet === 1 ? " foot" : " feet"}${
      inches > 0 ? ` ${inches} ${inches === 1 ? "inch" : "inches"}` : ""
    }`;
    weightDisplay = `${weight} ${weight === 1 ? "pound" : "pounds"}`;
  } else {
    const heightCm = parseFloat(document.getElementById("height-cm").value);
    const weight = parseFloat(document.getElementById("weight").value);

    heightDisplay = `${heightCm} centimeters`;
    weightDisplay = `${weight} kilograms`;
  }

  resultSection.innerHTML = `

    <div class="calculated-results">
      <div style='display:flex; justify-content:space-between; align-items:center;'>
        <h3>Calculated Results</h3>
        <button
          class="download-btn btn neon-pulse"
          onclick="downloadBMIResultsPDF()"
          title="Download results as PDF"
        >
          <span class="download-icon">📥</span>
          <span>Download PDF</span>
        </button>
      </div>
      <div class="bmi-display">
        <div class="bmi-value-section">
          <div class="bmi-value">${bmi.toFixed(1)}</div>
          <div class="bmi-label">BMI</div>
        </div>
        <div class="bmi-category-section">
          <div class="bmi-category" style="color: ${color}">${category}</div>
          <div class="bmi-category-label">BMI CATEGORY</div>
        </div>
      </div>
    </div>
    
    <div class="info-entered">
      <h3>Information Entered</h3>
      <div class="info-display">
        <strong>Height:</strong> ${heightDisplay}<br>
        <strong>Weight:</strong> ${weightDisplay}
      </div>
    </div>
      <div class="bmi-gauge-container"></div>
    
            <!-- BMI Color Legend -->
            <div class="bmi-legend" style="display: flex; justify-content: center; gap: 1.5rem; margin: 18px 0 0 0; align-items: center;flex-wrap:wrap;">
              <div style="display: flex; align-items: center; gap: 0.4rem;">
                <span style="display:inline-block;width:16px;height:16px;border-radius:50%;background:#e57373;"></span>
                <span style="font-size:0.95em;">Underweight</span>
              </div>
              <div style="display: flex; align-items: center; gap: 0.4rem;">
                <span style="display:inline-block;width:16px;height:16px;border-radius:50%;background:#4caf50;"></span>
                <span style="font-size:0.95em;">Normal</span>
              </div>
              <div style="display: flex; align-items: center; gap: 0.4rem;">
                <span style="display:inline-block;width:16px;height:16px;border-radius:50%;background:#ffd600;"></span>
                <span style="font-size:0.95em;">Overweight</span>
              </div>
              <div style="display: flex; align-items: center; gap: 0.4rem;">
                <span style="display:inline-block;width:16px;height:16px;border-radius:50%;background:#ef4444;"></span>
                <span style="font-size:0.95em;">Obesity</span>
              </div>
            </div>
    
    <div class="detailed-results">
      <h3>Detailed Results</h3>
      <p class="detailed-text">${description}</p>
      
      <table class="bmi-ranges-table">
        <thead>
          <tr>
            <th>BMI Category</th>
            <th>BMI Range</th>
          </tr>
        </thead>
        <tbody>
          <tr ${bmi < 18.5 ? 'class="current-category"' : ""}>
            <td>Underweight</td>
            <td>Below 18.5</td>
          </tr>
          <tr ${bmi >= 18.5 && bmi < 25 ? 'class="current-category"' : ""}>
            <td>Healthy Weight</td>
            <td>18.5 to 24.9</td>
          </tr>
          <tr ${bmi >= 25 && bmi < 30 ? 'class="current-category"' : ""}>
            <td>Overweight</td>
            <td>25.0 to 29.9</td>
          </tr>
          <tr ${bmi >= 30 ? 'class="current-category"' : ""}>
            <td>Obesity</td>
            <td>30.0 and Above</td>
          </tr>
        </tbody>
      </table>
    </div>
  `;
  // Animate the gauge pointer after rendering
  animateBMIGauge(bmi);
}


// Header scroll effect
function handleScroll() {
    const header = document.querySelector("header");
    if (header) {
      if (window.scrollY === 0) {
        header.classList.remove("scrolled");
      } else {
        header.classList.add("scrolled");
      }
    }
  }
  
  // Add scroll event listener
  window.addEventListener("scroll", handleScroll);
  
  // Initialize header state
  handleScroll();


  function downloadResultsPDF() {
    try {
      // Check if results are available
      const resultsContainer = document.getElementById("results-container");
      if (!resultsContainer || resultsContainer.style.display === "none") {
        alert("Please calculate your results first before downloading.");
        return;
      }

      // Initialize jsPDF
      const { jsPDF } = window.jspdf;
      const doc = new jsPDF();

      // Set up PDF styling - keeping original colors
      const primaryColor = [32, 110, 85]; // #206e55
      const textColor = [17, 24, 39]; // #111827
      const lightGray = [107, 114, 128]; // #6b7280
      const backgroundColor = [248, 250, 252]; // Light background
      const borderColor = [229, 231, 235]; // Light border

      // Page layout constants
      const pageWidth = 210;
      const leftMargin = 20;
      const rightMargin = 20;
      const contentWidth = pageWidth - leftMargin - rightMargin;

      // Add header with better spacing
      let yPosition = 25;
      doc.setFontSize(20);
      doc.setTextColor(...primaryColor);
      doc.setFont(undefined, "bold");
      doc.text("Daily Calorie Requirements", leftMargin, yPosition);

      yPosition += 12;
      doc.setFontSize(12);
      doc.setTextColor(...lightGray);
      doc.setFont(undefined, "normal");
      doc.text("Generated by MeetAugust Calculator", leftMargin, yPosition);

      // Add line separator with proper spacing
      yPosition += 8;
      doc.setDrawColor(...primaryColor);
      doc.setLineWidth(0.5);
      doc.line(leftMargin, yPosition, leftMargin + contentWidth, yPosition);

      yPosition += 20;

      // Get user input data
      const age = document.getElementById("age").value;
      const gender = document.querySelector(
        'input[name="gender"]:checked'
      )?.value;
      const activity = document.getElementById("activity").value;

      // Get height and weight based on current unit system
      const heightWeight = getCurrentHeightWeight();
      const unitSystem = settings.unitSystem;

      // Add user input section with better formatting
      doc.setFontSize(14);
      doc.setTextColor(...textColor);
      doc.setFont(undefined, "bold");
      doc.text("Your Information:", leftMargin, yPosition);
      yPosition += 15;

      // Format height and weight display
      let heightDisplay, weightDisplay;
      if (unitSystem === "us") {
        // Convert height from cm to feet and inches
        const totalInches = heightWeight.height / 2.54;
        const feet = Math.floor(totalInches / 12);
        const inches = Math.round(totalInches % 12);
        heightDisplay = `${feet}' ${inches}"`;
        // Convert weight from kg to lbs
        weightDisplay = `${Math.round(heightWeight.weight * 2.20462)} lbs`;
      } else {
        heightDisplay = `${Math.round(heightWeight.height)} cm`;
        weightDisplay = `${Math.round(heightWeight.weight)} kg`;
      }

      const userInfo = [
        `Age: ${age} years`,
        `Gender: ${
          gender
            ? gender.charAt(0).toUpperCase() + gender.slice(1)
            : "Not specified"
        }`,
        `Height: ${heightDisplay}`,
        `Weight: ${weightDisplay}`,
        `Activity Level: ${getActivityLevelText(activity)}`,
      ].filter(Boolean);

      // Create background box for user info
      const userInfoHeight = userInfo.length * 8 + 15;
      doc.setFillColor(...backgroundColor);
      doc.setDrawColor(...borderColor);
      doc.setLineWidth(0.3);
      doc.roundedRect(
        leftMargin,
        yPosition - 5,
        contentWidth,
        userInfoHeight,
        2,
        2,
        "FD"
      );

      // Add user info with consistent spacing
      yPosition += 5;
      doc.setFontSize(11);
      doc.setTextColor(...textColor);
      doc.setFont(undefined, "normal");

      userInfo.forEach((info) => {
        doc.text(info, leftMargin + 10, yPosition);
        yPosition += 8;
      });

      yPosition += 20;

      // Add results section with better formatting
      doc.setFontSize(14);
      doc.setTextColor(...textColor);
      doc.setFont(undefined, "bold");
      doc.text("Your Daily Calorie Targets:", leftMargin, yPosition);
      yPosition += 20;

      // Get results table data
      const resultsTable = document.querySelector(".results-table tbody");
      if (resultsTable) {
        const rows = resultsTable.querySelectorAll("tr");

        // Calculate table dimensions with proper spacing
        const tableHeight = rows.length * 22 + 25;

        // Create table background with rounded corners
        doc.setFillColor(255, 255, 255);
        doc.setDrawColor(...borderColor);
        doc.setLineWidth(0.5);
        doc.roundedRect(
          leftMargin,
          yPosition - 5,
          contentWidth,
          tableHeight,
          8,
          8,
          "FD"
        );

        yPosition += 15;

        rows.forEach((row, index) => {
          const goalCell = row.querySelector("td:first-child");
          const caloriesCell = row.querySelector("td:nth-child(2)");

          if (goalCell && caloriesCell) {
            const goal = goalCell.textContent.trim();
            const calories = caloriesCell.textContent.trim();

            // Extract calories number
            const caloriesNumber = calories.match(/[\d,]+/)?.[0] || calories;

            // Parse multi-line goals
            const goalLines = goal
              .split("\n")
              .map((line) => line.trim())
              .filter((line) => line);
            const mainGoal = goalLines[0];
            const subGoal = goalLines.length > 1 ? goalLines[1] : "";

            // Main goal name - left aligned
            doc.setFontSize(12);
            doc.setTextColor(...primaryColor);
            doc.setFont(undefined, "bold");
            doc.text(mainGoal, leftMargin + 20, yPosition);

            // Sub goal - smaller text, indented
            if (subGoal) {
              doc.setFontSize(10);
              doc.setTextColor(...primaryColor);
              doc.setFont(undefined, "normal");
              doc.text(subGoal, leftMargin + 35, yPosition + 8);
            }

            // Calories value - right aligned
            doc.setFontSize(14);
            doc.setTextColor(...textColor);
            doc.setFont(undefined, "bold");

            const caloriesText = caloriesNumber;
            const unitsText = " calories/day";

            // Calculate text widths for proper alignment
            const caloriesTextWidth = doc.getTextWidth(caloriesText);
            doc.setFontSize(10);
            const unitsTextWidth = doc.getTextWidth(unitsText);

            // Position from right edge with proper spacing
            const totalTextWidth = caloriesTextWidth + unitsTextWidth;
            const caloriesX = leftMargin + contentWidth - 20 - totalTextWidth;

            // Draw calories number
            doc.setFontSize(14);
            doc.setFont(undefined, "bold");
            doc.text(caloriesText, caloriesX, yPosition + (subGoal ? 4 : 0));

            // Draw units in smaller, lighter text
            doc.setFontSize(10);
            doc.setTextColor(...lightGray);
            doc.setFont(undefined, "normal");
            doc.text(
              unitsText,
              caloriesX + caloriesTextWidth,
              yPosition + (subGoal ? 4 : 0)
            );

            yPosition += 22;
          }
        });
      }

      // Add footer with better spacing
      yPosition += 30;

      // Add a subtle separator line
      doc.setDrawColor(...borderColor);
      doc.setLineWidth(0.3);
      doc.line(
        leftMargin,
        yPosition - 15,
        leftMargin + contentWidth,
        yPosition - 15
      );

      // Footer information with proper alignment
      doc.setFontSize(9);
      doc.setTextColor(...lightGray);
      doc.setFont(undefined, "normal");

      const generatedText = "Generated on: " + new Date().toLocaleDateString();
      const websiteText = "Visit: www.meetaugust.ai";

      doc.text(generatedText, leftMargin, yPosition);

      // Right align website text
      const websiteTextWidth = doc.getTextWidth(websiteText);
      doc.text(
        websiteText,
        leftMargin + contentWidth - websiteTextWidth,
        yPosition
      );

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 10);
      const filename = `calorie-requirements-${timestamp}.pdf`;

      // Save the PDF
      doc.save(filename);
    } catch (error) {
      console.error("Error generating PDF:", error);
      alert("Sorry, there was an error generating the PDF. Please try again.");
    }
  }

function downloadBMIResultsPDF() {
  try {
    const resultSection = document.getElementById("result-section");
    if (!resultSection) {
      alert("Please calculate your BMI first before downloading.");
      return;
    }
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    // Colors from new palette
    const primaryColor = [32, 110, 85];
    const textPrimary = [17, 24, 39];
    const textSecondary = [107, 114, 128];
    const borderColor = [229, 231, 235];
    let y = 20;
    doc.setFontSize(20);
    doc.setTextColor(...primaryColor);
    doc.setFont(undefined, "bold");
    doc.text("Adult BMI Calculator Results", 15, y);
    y += 10;
    doc.setFontSize(12);
    doc.setTextColor(...textSecondary);
    doc.setFont(undefined, "normal");
    doc.text("Generated by MeetAugust Calculator", 15, y);
    y += 8;
    doc.setDrawColor(...primaryColor);
    doc.setLineWidth(0.5);
    doc.line(15, y, 195, y);
    y += 10;
    // Get BMI value, category, and description
    const bmiValue = document.querySelector(".bmi-value")?.textContent || "-";
    const bmiCategory = document.querySelector(".bmi-category")?.textContent || "-";
    const bmiDescription = document.querySelector(".detailed-text")?.textContent || "-";
    // Get entered info
    const infoDisplay = document.querySelector(".info-display")?.innerText || "-";
    // User Info
    doc.setFontSize(14);
    doc.setTextColor(...textPrimary);
    doc.setFont(undefined, "bold");
    doc.text("Your Information:", 15, y);
    y += 8;
    doc.setFontSize(11);
    doc.setFont(undefined, "normal");
    infoDisplay.split("\n").forEach(line => {
      doc.text(line, 20, y);
      y += 6;
    });
    y += 4;
    // BMI Results
    doc.setFontSize(14);
    doc.setFont(undefined, "bold");
    doc.text("BMI Results:", 15, y);
    y += 8;
    doc.setFontSize(12);
    doc.setFont(undefined, "normal");
    doc.text(`BMI Value: ${bmiValue}`, 20, y);
    y += 6;
    doc.text(`Category: ${bmiCategory}`, 20, y);
    y += 6;
    doc.text(bmiDescription, 20, y, { maxWidth: 170 });
    y += 20;
    // BMI Ranges Table
    doc.setFontSize(13);
    doc.setFont(undefined, "bold");
    doc.text("BMI Categories:", 15, y);
    y += 7;
    doc.setFontSize(11);
    doc.setFont(undefined, "normal");
    const table = [
      ["Underweight", "Below 18.5"],
      ["Healthy Weight", "18.5 to 24.9"],
      ["Overweight", "25.0 to 29.9"],
      ["Obesity", "30.0 and Above"]
    ];
    table.forEach(([cat, range]) => {
      doc.text(cat, 20, y);
      doc.text(range, 80, y);
      y += 6;
    });
    y += 10;
    doc.setDrawColor(...borderColor);
    doc.setLineWidth(0.3);
    doc.line(15, y, 195, y);
    y += 8;
    doc.setFontSize(9);
    doc.setTextColor(...textSecondary);
    doc.text("Generated on: " + new Date().toLocaleDateString(), 15, y);
    const websiteText = "Visit: www.meetaugust.ai";
    const websiteTextWidth = doc.getTextWidth(websiteText);
    doc.text(websiteText, 195 - websiteTextWidth, y);
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `bmi-results-${timestamp}.pdf`;
    doc.save(filename);
  } catch (error) {
    console.error("Error generating PDF:", error);
    alert("Sorry, there was an error generating the PDF. Please try again.");
  }
}