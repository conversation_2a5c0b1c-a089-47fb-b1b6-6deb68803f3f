<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>무료 임신 계산기 - 출산 예정일 및 임신 주수 계산기 2025</title>
    <meta name="description" content="무료 정확한 임신 계산기로 출산 예정일과 임신 주수를 계산하세요. 최종 월경일을 기준으로 임신 여정을 주별로 추적해보세요." />
    <meta name="keywords" content="임신 계산기, 출산 예정일 계산기, 임신 주수 계산기, 임신 추적기, 임신 주차, 마지막 월경 계산기, 임신 출산일, 임신부" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="무료 임신 계산기 - 출산 예정일 및 임신 주수 계산기" />
    <meta property="og:description" content="무료 정확한 임신 계산기로 출산 예정일과 임신 주수를 계산하세요. 주별로 임신 여정을 추적해보세요." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/ko/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/ko/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/ko/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/ko/calculators/">
            <img
              width="200"
              src="/ko/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="계산기 로고"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >August와 대화하기</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>무료 임신 계산기 및 출산 예정일 계산기</h1>
        <p>정확하고 사용하기 쉬운 임신 계산기로 출산 예정일을 계산하고 임신 주수를 추적하세요</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">전 세계 임신부와 의료진이 신뢰하는 계산기</span>
          </div>

          <h2>왜 저희 임신 계산기를 사용해야 할까요?</h2>
          <p>저희 무료 임신 계산기는 최종 월경일(LMP)을 기반으로 아기의 출산 예정일과 현재 임신 주수를 계산해드립니다. 임신 초기든 임신 여정을 추적하고 있든, 이 도구는 정확한 계산을 통해 아기의 출산을 계획하는 데 도움을 줍니다.</p>

          <h3>주요 기능:</h3>
          <ul>
            <li>마지막 월경일 기반 즉시 출산 예정일 계산</li>
            <li>현재 임신 주수(주 및 일)</li>
            <li>드롭다운 메뉴로 쉽게 사용 가능한 인터페이스</li>
            <li>WHO 지침을 따르는 의학적으로 정확한 계산</li>
            <li>등록 없이 무료로 사용 가능</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">출산 예정일 계산하기</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">오늘 날짜:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="현재 날짜" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">최종 월경 첫날 (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="LMP 날짜" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                오늘로 설정
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                결과 지우기
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">예상 출산 예정일:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  현재 임신 주수:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>임신 계산기에 대한 자주 묻는 질문</h2>

        <div class="faq-item">
          <div class="faq-question">이 임신 계산기는 얼마나 정확한가요?</div>
          <div class="faq-answer">저희 임신 계산기는 최종 월경일에 280일을 더하는 표준 의학 공식을 사용합니다. 이 방법은 출산 예정일 예측에 약 95% 정확하지만, 개인별 임신은 최대 2주까지 차이가 날 수 있습니다.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">정확한 LMP 날짜를 기억하지 못하면 어떻게 하나요?</div>
          <div class="faq-answer">정확한 LMP 날짜를 기억하지 못한다면, 최대한 가깝게 추정해보세요. 의료진은 첫 산전 검진 시 초음파 측정을 통해 더 정확한 출산 예정일을 제공할 수 있습니다.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            생리가 불규칙한 경우에도 이 계산기를 사용할 수 있나요?
          </div>
          <div class="faq-answer">생리 주기가 불규칙하다면 이 계산기는 덜 정확할 수 있습니다. 이런 경우 의사는 초음파 측정을 통해 더 정확한 출산 예정일을 결정할 것입니다.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            첫 산전 진료 예약은 언제 해야 하나요?
          </div>
          <div class="faq-answer">
            대부분의 의료진은 임신 6-8주 사이에 첫 산전 진료를 예약할 것을 권장합니다. 저희 계산기를 사용하여 현재 임신 주수를 확인하고 그에 따라 계획하세요.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            임신 주수와 태아 주수의 차이는 무엇인가요?
          </div>
          <div class="faq-answer">임신 주수는 최종 월경일부터 계산되며, 태아 주수는 수정일부터 계산됩니다(일반적으로 2주 후). 의료진은 일관성을 위해 일반적으로 임신 주수를 사용합니다.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
