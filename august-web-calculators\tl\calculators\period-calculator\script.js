const locales = [
  "en",
  "fr",
  "de",
  "es",
  "it",
  "pt",
  "ru",
  "ja",
  "ko",
  "he",
  "bg",
  "fi",
  "hr",
  "lv",
  "mk",
  "mr",
  "sk",
  "sl",
  "sr",
  "tl",
  "el",
];

const languageNames = {
  en: "English",
  fr: "Français",
  de: "Deutsch",
  es: "Español",
  it: "Italiano",
  pt: "Português",
  ru: "Русский",
  ja: "日本語",
  ko: "한국어",
  he: "עברית",
  bg: "Български",
  fi: "Suomi",
  hr: "Hrvatsk<PERSON>",
  lv: "Latvie<PERSON><PERSON>",
  mk: "Македонски",
  mr: "मराठी",
  sk: "Slovenčina",
  sl: "Slovenščina",
  sr: "Српски",
  tl: "Tagalog",
  el: "Ελληνικά",
};

const select = document.getElementById("language-select");
const currentLang = window.location.pathname.split("/")[1] || "en";

locales.forEach((l) => {
  const opt = document.createElement("option");
  opt.value = l;
  opt.textContent = languageNames[l] || l;
  if (l === currentLang) opt.selected = true;
  select.appendChild(opt);
});

select.addEventListener("change", function () {
  const newLang = this.value;
  let path = window.location.pathname.split("/");
  if (locales.includes(path[1])) {
    path[1] = newLang;
  } else {
    path = ["", newLang, ...path.slice(1)];
  }
  window.preferredLang = newLang;
  window.location.pathname = path.join("/");
});

if (window.preferredLang && window.preferredLang !== currentLang) {
  select.value = window.preferredLang;
  select.dispatchEvent(new Event("change"));
}

function handleScroll() {
  const header = document.querySelector("header");
  if (header) {
    if (window.scrollY === 0) {
      header.classList.remove("scrolled");
    } else {
      header.classList.add("scrolled");
    }
  }
}

window.addEventListener("scroll", handleScroll);
handleScroll();

document.getElementById("trackerForm").addEventListener("submit", function (e) {
  e.preventDefault();
  const lastPeriodDate = document.getElementById("lastPeriod").value;
  const periodLength = parseInt(
    document.getElementById("periodLength").value,
    10
  );
  const cycleLength = parseInt(
    document.getElementById("cycleLength").value,
    10
  );
  if (
    !lastPeriodDate ||
    isNaN(periodLength) ||
    isNaN(cycleLength) ||
    periodLength < 1 ||
    periodLength > 15 ||
    cycleLength < 21 ||
    cycleLength > 45
  ) {
    alert("Please enter valid values in all fields.");
    return;
  }
  renderTracker(new Date(lastPeriodDate), periodLength, cycleLength);
  setTimeout(() => {
    document.getElementById("resultsSection").style.display = "block";
    document.getElementById("resultsSection").scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  }, 100);
});

document.getElementById("resetBtn").addEventListener("click", function () {
  document.getElementById("trackerForm").reset();
  document.getElementById("resultsSection").style.display = "none";
  document.getElementById("calendarContainer").innerHTML = "";
});

function getPhase(date, lastPeriod, cycleLength, periodLength) {
  const msPerDay = 1000 * 60 * 60 * 24;
  const d = new Date(date.getTime());
  d.setHours(0, 0, 0, 0);
  const lp = new Date(lastPeriod.getTime());
  lp.setHours(0, 0, 0, 0);
  const daysSince = Math.floor((d - lp) / msPerDay);
  let cycleDay = daysSince % cycleLength;
  if (cycleDay < 0) {
    cycleDay += cycleLength; // Handle dates before last period
  }

  // Period: First 'periodLength' days of the cycle
  if (cycleDay < periodLength) {
    return "period";
  }
  // Pre-period: 2 days before the period starts
  if (cycleDay >= cycleLength - 2 && cycleDay < cycleLength) {
    return "pre-period";
  }
  // Post-period: 2 days after the period ends
  if (cycleDay >= periodLength && cycleDay < periodLength + 2) {
    return "post-period";
  }
  // Ovulation: Days 10 to 14 of the cycle
  if (cycleDay >= 10 && cycleDay < 15) {
    return "ovulation";
  }
  // Other days
  return "normal";
}

function generateCalendar(year, month, lastPeriod, periodLength, cycleLength) {
  const firstDay = new Date(year, month, 1).getDay(); // 0 = Sunday
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  let html = '<table class="calendar-table" style="margin: 0 auto;">';
  html +=
    "<tr><th>Sun</th><th>Mon</th><th>Tue</th><th>Wed</th><th>Thu</th><th>Fri</th><th>Sat</th></tr>";
  let day = 1;
  for (let week = 0; week < 6; week++) {
    html += "<tr>";
    for (let dow = 0; dow < 7; dow++) {
      if ((week === 0 && dow < firstDay) || day > daysInMonth) {
        html += "<td></td>";
      } else {
        const date = new Date(year, month, day);
        const phase = getPhase(date, lastPeriod, cycleLength, periodLength);
        html += `<td><div class="day ${phase}">${day}</div></td>`;
        day++;
      }
    }
    html += "</tr>";
    if (day > daysInMonth) break;
  }
  html += "</table>";
  return html;
}

function renderTracker(lastPeriod, periodLength, cycleLength) {
  let months = [];
  for (let i = 0; i < 3; i++) {
    let d = new Date(lastPeriod.getFullYear(), lastPeriod.getMonth() + i, 1);
    months.push(d);
  }

  let calendarHTML = '<div class="calendars">';
  months.forEach((month) => {
    let monthStr = month.toLocaleString("default", {
      month: "long",
      year: "numeric",
    });
    calendarHTML += `<div class="calendar"><h3>${monthStr}</h3>`;
    calendarHTML += generateCalendar(
      month.getFullYear(),
      month.getMonth(),
      lastPeriod,
      periodLength,
      cycleLength
    );
    calendarHTML += "</div>";
  });
  calendarHTML += "</div>";

  document.getElementById("calendarContainer").innerHTML = calendarHTML;
  document.getElementById("resultsSection").style.display = "block";
}
