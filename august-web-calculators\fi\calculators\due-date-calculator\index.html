<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ilmainen Raskauslaskuri - Synnytyspäivän ja Raskausiän Laskuri 2025</title>
    <meta name="description" content="Laske synnytyspäiväsi ja raskausikäsi ilmaisella ja tarkalla raskauslaskurillamme. Seuraa raskauttasi viikko viikolta viimeisen kuukautisesi perusteella." />
    <meta name="keywords" content="raskauslaskuri, synnyt<PERSON>p<PERSON>iv<PERSON><PERSON><PERSON><PERSON>, rask<PERSON><PERSON><PERSON><PERSON> lasku<PERSON>, raskaud<PERSON> seuranta, rask<PERSON><PERSON><PERSON><PERSON>, L<PERSON>-lasku<PERSON>, synnytyspäivä, odottavat äidit" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Ilmainen Raskauslaskuri - Synnytyspäivän ja Raskausiän Laskuri" />
    <meta property="og:description" content="Laske synnytyspäiväsi ja raskausikäsi ilmaisella ja tarkalla raskauslaskurillamme. Seuraa raskauttasi viikko viikolta." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/fi/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/fi/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/fi/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/fi/calculators/">
            <img
              width="200"
              src="/fi/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Laskurin logo"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Keskustele Augustin kanssa</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Laskettu aika -laskuri</h1>
        <p>Arvioi vauvasi laskettu aika viimeisten kuukautisten tai hedelmöityspäivän perusteella. Käytä laskettu aika -laskuriamme raskauden suunnitteluun ja valmistautumiseen vauvan saapumiseen.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Luottavat odottavat äidit ja terveydenhuollon ammattilaiset maailmanlaajuisesti</span>
          </div>

          <h2>Miksi käyttää raskauslaskuriamme?</h2>
          <p>Ilmainen raskauslaskurimme auttaa sinua määrittämään vauvasi synnytyspäivän ja nykyisen raskausiän viimeisen kuukautisesi (LMP) perusteella. Olitpa juuri raskaana tai seuraat raskauttasi, tämä työkalu tarjoaa tarkkoja laskelmia auttaaksesi sinua suunnittelemaan vauvasi saapumista.</p>

          <h3>Tärkeimmät ominaisuudet:</h3>
          <ul>
            <li>Välitön synnytyspäivän laskenta LMP:n perusteella</li>
            <li>Nykyinen raskausikä viikoissa ja päivissä</li>
            <li>Helppokäyttöinen käyttöliittymä pudotusvalikoilla</li>
            <li>Lääketieteellisesti tarkat laskelmat WHO:n ohjeiden mukaisesti</li>
            <li>Ilmainen käyttää ilman rekisteröitymistä</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Laske synnytyspäiväsi</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Tämän päivän päivämäärä:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Nykyinen päivämäärä" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Viimeisen kuukautisen ensimmäinen päivä (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="LMP-päivämäärä" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Aseta tähän päivään
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Tyhjennä tulokset
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Arvioitu synnytyspäiväsi:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Nykyinen raskausikä:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Usein kysytyt kysymykset raskauslaskureista</h2>

        <div class="faq-item">
          <div class="faq-question">Kuinka tarkka tämä raskauslaskuri on?</div>
          <div class="faq-answer">Raskauslaskurimme käyttää standardia lääketieteellistä kaavaa, joka lisää 280 päivää viimeiseen kuukautiseesi. Tämä menetelmä on noin 95 % tarkka synnytyspäivän ennustamisessa, vaikka yksittäiset raskaudet voivat vaihdella jopa kahdella viikolla.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Entä jos en muista tarkkaa LMP-päivämäärääni?</div>
          <div class="faq-answer">Jos et muista tarkkaa LMP-päivämäärääsi, yritä arvioida mahdollisimman tarkasti. Terveydenhuollon tarjoajasi voi käyttää ultraäänimittauksia antaakseen tarkemman synnytyspäivän ensimmäisen prenataalivierailusi aikana.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Voinko käyttää tätä laskuria, jos minulla on epäsäännölliset kuukautiset?
          </div>
          <div class="faq-answer">Jos sinulla on epäsäännölliset kuukautiskierrot, tämä laskuri voi olla vähemmän tarkka. Tällaisissa tapauksissa lääkärisi käyttää todennäköisesti ultraäänidatointia synnytyspäivän tarkempaan määrittämiseen.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Milloin minun pitäisi varata ensimmäinen prenataalikäyntini?
          </div>
          <div class="faq-answer">
            Useimmat terveydenhuollon tarjoajat suosittelevat ensimmäisen prenataalikäynnin varaamista raskauden 6–8 viikon välillä. Käytä laskuriamme määrittääksesi nykyisen raskausikäsi ja suunnittele sen mukaisesti.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Mitä eroa on raskausiällä ja sikiön iällä?
          </div>
          <div class="faq-answer">Raskausikä lasketaan viimeisestä kuukautisestasi, kun taas sikiön ikä lasketaan hedelmöityksestä (yleensä 2 viikkoa myöhemmin). Lääketieteen ammattilaiset käyttävät yleensä raskausikää yhtenäisyyden vuoksi.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
