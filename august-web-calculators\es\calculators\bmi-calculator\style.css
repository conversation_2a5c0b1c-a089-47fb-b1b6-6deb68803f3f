:root {
    --primary-color: #206e55;
    --primary-dark: #2d4a3a;
    --primary-light: #f0f9f4;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --background-light: #f8fafc;
    --background-white: #ffffff;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.2s ease;
  }
  
  * {
    box-sizing: border-box;
  }
  
  body {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    color: var(--text-primary);
    line-height: 1.7;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  #container {
    max-width: 1350px;
    margin: 0 auto;
  }
  header {
    background-color: #ffffff;
    padding: 4px 0px;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 1000;
  }
  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    margin: 0 auto;
  }
  .logo {
    display: flex;
    align-items: center;
  }
  .logo img {
    height: 60px;
    width: auto;
  }
  .nav {
    display: flex;
    align-items: center;
    gap: 32px;
  }
  .nav-links {
    display: flex;
    align-items: center;
    gap: 24px;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .nav-links a {
    color: #111827;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.2s ease;
  }
  .nav-links a:hover {
    color: #416955;
  }
  .nav-links a.active {
    color: #416955;
  }
  .talk-to-august {
    background-color: #416955;
    color: white !important;
    padding: 10px 18px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: background-color 0.2s ease;
  }
  .talk-to-august:hover {
    background-color: #2d4a3a;
    color: white !important;
  }
  main {
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 0 20px;
  }
  
  /* BMI Calculator Styles */
  .page-header {
    margin-bottom: 40px;
  }
  
  .page-title {
    font-size: 48px;
    font-weight: 400;
    color: var(--text-muted);
    margin: 0 0 20px 0;
    letter-spacing: -0.5px;
  }
  
  .page-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
  }
  
  .info-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 14px;
  }
  
  .shield-icon {
    width: 20px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
  }
  
  .content-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    align-items: start;
  }
  
  .info-section {
    background-color: var(--background-white);
    padding: 40px;
    border-radius: var(--border-radius-lg);
    max-width: 56%;
    box-shadow: var(--shadow-md);
    transition: max-width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      width 0.3s cubic-bezier(0.4, 0, 0.2, 1), padding 0.3s;
  }
  .info-section.full-width-info {
    max-width: 100% !important;
    width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
  
  .info-section h2 {
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0 0 20px 0;
  }
  
  .info-text {
    color: var(--text-secondary);
    font-size: 18px;
    line-height: 1.6;
    margin: 0 0 30px 0;
  }
  
  .calculator-section {
    background: var(--background-light);
    color: var(--text-primary);
    padding: 40px;
    width: 40%;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
  }
  
  
  .download-btn {
    padding: 14px 16px;
    font-size: 12px;
    gap: 6px;
    border-radius: 50%;
    border: none;
    cursor: pointer;

  }

  

  .download-btn span:last-child {
    display: none;
  }

  .download-icon {
    font-size: 18px;
  }
  .calculator-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 30px 0;
    color: var(--primary-color);
  }
  
  .unit-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    align-items: center;
  }
  
  .unit-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }
  
  .unit-option input[type="radio"] {
    width: 20px;
    height: 20px;
    accent-color: white;
  }
  
  .unit-option label {
    color: white;
    font-weight: 500;
    cursor: pointer;
  }
  
  .reset-link {
    color: white;
    text-decoration: underline;
    font-size: 14px;
    margin-left: auto;
    cursor: pointer;
    font-weight: 500;
  }
  
  .reset-link:hover {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .form-group {
    margin-bottom: 25px;
  }
  
  .form-label {
    display: block;
    color: white;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .input-group {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  
  .form-input {
    padding: 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 16px;
    width: 120px;
    transition: var(--transition);
  }
  
  .form-input:focus {
    outline: none;
    border-color: white;
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .form-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
  }
  
  .unit-label {
    color: white;
    font-weight: 500;
    white-space: nowrap;
  }
  
  .calculate-btn {
    width: 100%;
    padding: 15px;
    background-color: rgba(255, 255, 255, 0.2);
    border: 2px solid white;
    border-radius: var(--border-radius);
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 10px;
  }
  
  .calculate-btn:hover {
    background-color: white;
    color: var(--primary-color);
  }
  
  .result-section {
    margin-top: 30px;
    background-color: var(--background-white);
    border-radius: var(--border-radius);
    display: none;
    color: var(--text-primary);
    box-shadow: var(--shadow-md);
  }
  
  .result-section.show {
    display: block;
  }
  
  .calculated-results {
    background-color: var(--primary-light);
    padding: 20px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: 1px solid var(--border-color);
  }
  
  .calculated-results h3 {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
  }
  
  .bmi-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .bmi-value-section {
    text-align: center;
  }
  
  .bmi-value {
    font-size: 48px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1;
  }
  
  .bmi-label {
    color: var(--text-muted);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 5px;
  }
  
  .bmi-category-section {
    text-align: right;
  }
  
  .bmi-category {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    line-height: 1;
  }
  
  .bmi-category-label {
    color: var(--text-muted);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 5px;
  }
  
  .info-entered {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .info-entered h3 {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 10px 0;
  }
  
  .info-display {
    color: var(--text-secondary);
    font-size: 14px;
  }
  
  .detailed-results {
    padding: 20px;
  }
  
  .detailed-results h3 {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
  }
  
  .detailed-text {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 20px;
  }
  
  .bmi-ranges-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  }
  
  .bmi-ranges-table th {
    background-color: var(--primary-color);
    color: white;
    padding: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: 600;
  }
  
  .bmi-ranges-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
    color: var(--text-secondary);
  }
  
  .bmi-ranges-table tr:nth-child(even) {
    background-color: var(--background-light);
  }
  
  .current-category {
    background-color: var(--primary-light) !important;
    font-weight: 600;
    color: var(--primary-color) !important;
  }
  
  .current-category::before {
    content: "▶ ";
    color: var(--primary-color);
    font-weight: bold;
  }
  
  .disclaimer {
    background-color: var(--background-light);
    padding: 15px;
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.5;
  }
  
  .note-section {
    background-color: var(--primary-light);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-top: 40px;
    border-left: 4px solid var(--primary-color);
  }
  
  .note-section p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
  }
  
  .note-section a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
  }
  
  .note-section a:hover {
    text-decoration: underline;
  }
  
  .weight-image {
    width: 100%;
    max-width: 400px;
    height: auto;
    margin-top: 20px;
    border-radius: var(--border-radius);
  }
  @media (max-width: 1280px) {
    .info-section {
      max-width: 100%;
    }
    .calculator-section {
      min-width: 100%;
    }
  }
  
  @media (max-width: 768px) {
    .content-grid {
      grid-template-columns: 1fr;
      gap: 30px;
  
      flex-direction: column-reverse;
    }
    .info-section {
      max-width: 100%;
    }
  
    .page-title {
      font-size: 36px;
    }
  
    .calculator-section,
    .info-section {
      padding: 30px 20px;
    }
  
    .input-group {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  
    .form-input {
      width: 100%;
    }
  
    .unit-selector {
      justify-content: center;
    }
  }
  
header {
    background-color: transparent;
    padding: 8px 0px;
    border-bottom: none;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: none;
  }
  
  header.scrolled {
    background-color: #f8f9fa;
    padding: 4px 0px;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    /* max-width: 1430px; */
    margin: 0 auto;
  }
  .logo {
    display: flex;
    align-items: center;
  }
  .logo img {
    height: 60px;
    width: auto;
  }
  .nav {
    display: flex;
    align-items: center;
    gap: 32px;
  }
  .nav-links {
    display: flex;
    align-items: center;
    gap: 24px;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .nav-links a {
    color: #111827;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.2s ease;
  }
  .nav-links a:hover {
    color: #206e55;
  }
  .nav-links a.active {
    color: #206e55;
  }
  .talk-to-august {
    background-color: #206e55;
    color: white !important;
    padding: 10px 18px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: background-color 0.2s ease;
  }
  .talk-to-august:hover {
    background-color: #1a5a47;
    color: white !important;
  }
  .calculator-title {
    color: var(--primary-color);
  }
  .unit-selector, .form-label, .unit-option label, .reset-link, .form-input, .unit-label, .calculate-btn {
    color: var(--text-primary);
    background: white;
    border-color: var(--border-color);
  }
  .calculate-btn {
    background: var(--primary-color);
    color: white;
  }
  .calculate-btn:hover {
    background: #1a5a47;
    color: white;
  }
.bmi-gauge-container {
  width: 100%;
  max-width: 180px;
  margin: 0 auto 18px auto;
  display: flex;
  justify-content: center;
  align-items: center;
  /* background: #f8fafc; */
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(32, 110, 85, 0.07);
  padding: 8px 0 2px 0;
}
.bmi-gauge-container svg {
  width: 100%;
  max-width: 160px;
  height: auto;
  display: block;
}
.bmi-legend {
  display: flex !important;
  justify-content: center;
  gap: 1.5rem;
  margin: 18px 0 0 0;
  align-items: center;
  background: none !important;
  z-index: 10;
  position: relative;
  opacity: 1 !important;
  visibility: visible !important;
}
.bmi-legend span {
  background: none;
  color: #222;
}