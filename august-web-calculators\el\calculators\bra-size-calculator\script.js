
const sizeChartInches = [
  {ubMin:23, ubMax:24, obMin:28, obMax:29, size:"28A"},
  {ubMin:23, ubMax:24, obMin:29, obMax:30, size:"28B"},
  {ubMin:23, ubMax:24, obMin:30, obMax:31, size:"28C"},
  {ubMin:23, ubMax:24, obMin:31, obMax:32, size:"28D"},
  {ubMin:23, ubMax:24, obMin:32, obMax:33, size:"28DD/E"},
  {ubMin:23, ubMax:24, obMin:33, obMax:34, size:"28F"},
  {ubMin:23, ubMax:24, obMin:34, obMax:35, size:"28G"},
  {ubMin:25, ubMax:26, obMin:30, obMax:31, size:"30A"},
  {ubMin:25, ubMax:26, obMin:31, obMax:32, size:"30B"},
  {ubMin:25, ubMax:26, obMin:32, obMax:33, size:"30C"},
  {ubMin:25, ubMax:26, obMin:33, obMax:34, size:"30D"},
  {ubMin:25, ubMax:26, obMin:34, obMax:35, size:"30DD/E"},
  {ubMin:25, ubMax:26, obMin:35, obMax:36, size:"30F"},
  {ubMin:25, ubMax:26, obMin:36, obMax:37, size:"30G"},
  {ubMin:27, ubMax:28, obMin:32, obMax:33, size:"32A"},
  {ubMin:27, ubMax:28, obMin:33, obMax:34, size:"32B"},
  {ubMin:27, ubMax:28, obMin:34, obMax:35, size:"32C"},
  {ubMin:27, ubMax:28, obMin:35, obMax:36, size:"32D"},
  {ubMin:27, ubMax:28, obMin:36, obMax:37, size:"32DD/E"},
  {ubMin:27, ubMax:28, obMin:37, obMax:38, size:"32F"},
  {ubMin:27, ubMax:28, obMin:38, obMax:39, size:"32G"},
  {ubMin:29, ubMax:30, obMin:34, obMax:35, size:"34A"},
  {ubMin:29, ubMax:30, obMin:35, obMax:36, size:"34B"},
  {ubMin:29, ubMax:30, obMin:36, obMax:37, size:"34C"},
  {ubMin:29, ubMax:30, obMin:37, obMax:38, size:"34D"},
  {ubMin:29, ubMax:30, obMin:38, obMax:39, size:"34DD/E"},
  {ubMin:29, ubMax:30, obMin:39, obMax:40, size:"34F"},
  {ubMin:29, ubMax:30, obMin:40, obMax:41, size:"34G"},
  {ubMin:31, ubMax:32, obMin:36, obMax:37, size:"36A"},
  {ubMin:31, ubMax:32, obMin:37, obMax:38, size:"36B"},
  {ubMin:31, ubMax:32, obMin:38, obMax:39, size:"36C"},
  {ubMin:31, ubMax:32, obMin:39, obMax:40, size:"36D"},
  {ubMin:31, ubMax:32, obMin:40, obMax:41, size:"36DD/E"},
  {ubMin:31, ubMax:32, obMin:41, obMax:42, size:"36F"},
  {ubMin:31, ubMax:32, obMin:42, obMax:43, size:"36G"},
  {ubMin:33, ubMax:34, obMin:38, obMax:39, size:"38A"},
  {ubMin:33, ubMax:34, obMin:39, obMax:40, size:"38B"},
  {ubMin:33, ubMax:34, obMin:40, obMax:41, size:"38C"},
  {ubMin:33, ubMax:34, obMin:41, obMax:42, size:"38D"},
  {ubMin:33, ubMax:34, obMin:42, obMax:43, size:"38DD/E"},
  {ubMin:33, ubMax:34, obMin:43, obMax:44, size:"38F"},
  {ubMin:33, ubMax:34, obMin:44, obMax:45, size:"38G"},
  {ubMin:35, ubMax:36, obMin:40, obMax:41, size:"40A"},
  {ubMin:35, ubMax:36, obMin:41, obMax:42, size:"40B"},
  {ubMin:35, ubMax:36, obMin:42, obMax:43, size:"40C"},
  {ubMin:35, ubMax:36, obMin:43, obMax:44, size:"40D"},
  {ubMin:35, ubMax:36, obMin:44, obMax:45, size:"40DD/E"},
  {ubMin:35, ubMax:36, obMin:45, obMax:46, size:"40F"},
  {ubMin:35, ubMax:36, obMin:46, obMax:47, size:"40G"},
  {ubMin:37, ubMax:38, obMin:42, obMax:43, size:"42A"},
  {ubMin:37, ubMax:38, obMin:43, obMax:44, size:"42B"},
  {ubMin:37, ubMax:38, obMin:44, obMax:45, size:"42C"},
  {ubMin:37, ubMax:38, obMin:45, obMax:46, size:"42D"},
  {ubMin:37, ubMax:38, obMin:46, obMax:47, size:"42DD/E"},
  {ubMin:37, ubMax:38, obMin:47, obMax:48, size:"42F"},
  {ubMin:37, ubMax:38, obMin:48, obMax:49, size:"42G"},
  {ubMin:39, ubMax:40, obMin:44, obMax:45, size:"44A"},
  {ubMin:39, ubMax:40, obMin:45, obMax:46, size:"44B"},
  {ubMin:39, ubMax:40, obMin:46, obMax:47, size:"44C"},
  {ubMin:39, ubMax:40, obMin:47, obMax:48, size:"44D"},
  {ubMin:39, ubMax:40, obMin:48, obMax:49, size:"44DD/E"},
  {ubMin:39, ubMax:40, obMin:49, obMax:50, size:"44F"},
  {ubMin:39, ubMax:40, obMin:50, obMax:51, size:"44G"},
];

const sizeChartCm = [
  {ubMin:58.5, ubMax:61, obMin:71.5, obMax:74, size:"28A"},
  {ubMin:58.5, ubMax:61, obMin:74, obMax:76.5, size:"28B"},
  {ubMin:58.5, ubMax:61, obMin:76.5, obMax:79, size:"28C"},
  {ubMin:58.5, ubMax:61, obMin:79, obMax:81.5, size:"28D"},
  {ubMin:58.5, ubMax:61, obMin:81.5, obMax:84, size:"28DD/E"},
  {ubMin:58.5, ubMax:61, obMin:84, obMax:86.5, size:"28F"},
  {ubMin:58.5, ubMax:61, obMin:86.5, obMax:89, size:"28G"},
  {ubMin:63.5, ubMax:66, obMin:76.5, obMax:79, size:"30A"},
  {ubMin:63.5, ubMax:66, obMin:79, obMax:81.5, size:"30B"},
  {ubMin:63.5, ubMax:66, obMin:81.5, obMax:84, size:"30C"},
  {ubMin:63.5, ubMax:66, obMin:84, obMax:86.5, size:"30D"},
  {ubMin:63.5, ubMax:66, obMin:86.5, obMax:89, size:"30DD/E"},
  {ubMin:63.5, ubMax:66, obMin:89, obMax:91.5, size:"30F"},
  {ubMin:63.5, ubMax:66, obMin:91.5, obMax:94, size:"30G"},
  {ubMin:68.5, ubMax:71, obMin:81.5, obMax:84, size:"32A"},
  {ubMin:68.5, ubMax:71, obMin:84, obMax:86.5, size:"32B"},
  {ubMin:68.5, ubMax:71, obMin:86.5, obMax:89, size:"32C"},
  {ubMin:68.5, ubMax:71, obMin:89, obMax:91.5, size:"32D"},
  {ubMin:68.5, ubMax:71, obMin:91.5, obMax:94, size:"32DD/E"},
  {ubMin:68.5, ubMax:71, obMin:94, obMax:96.5, size:"32F"},
  {ubMin:68.5, ubMax:71, obMin:96.5, obMax:99, size:"32G"},
  {ubMin:73.5, ubMax:76, obMin:86.5, obMax:89, size:"34A"},
  {ubMin:73.5, ubMax:76, obMin:89, obMax:91.5, size:"34B"},
  {ubMin:73.5, ubMax:76, obMin:91.5, obMax:94, size:"34C"},
  {ubMin:73.5, ubMax:76, obMin:94, obMax:96.5, size:"34D"},
  {ubMin:73.5, ubMax:76, obMin:96.5, obMax:99, size:"34DD/E"},
  {ubMin:73.5, ubMax:76, obMin:99, obMax:101.5, size:"34F"},
  {ubMin:73.5, ubMax:76, obMin:101.5, obMax:104, size:"34G"},
  {ubMin:78.5, ubMax:81, obMin:91.5, obMax:94, size:"36A"},
  {ubMin:78.5, ubMax:81, obMin:94, obMax:96.5, size:"36B"},
  {ubMin:78.5, ubMax:81, obMin:96.5, obMax:99, size:"36C"},
  {ubMin:78.5, ubMax:81, obMin:99, obMax:101.5, size:"36D"},
  {ubMin:78.5, ubMax:81, obMin:101.5, obMax:104, size:"36DD/E"},
  {ubMin:78.5, ubMax:81, obMin:104, obMax:106.5, size:"36F"},
  {ubMin:78.5, ubMax:81, obMin:106.5, obMax:109, size:"36G"},
  {ubMin:83.5, ubMax:86, obMin:96.5, obMax:99, size:"38A"},
  {ubMin:83.5, ubMax:86, obMin:99, obMax:101.5, size:"38B"},
  {ubMin:83.5, ubMax:86, obMin:101.5, obMax:104, size:"38C"},
  {ubMin:83.5, ubMax:86, obMin:104, obMax:106.5, size:"38D"},
  {ubMin:83.5, ubMax:86, obMin:106.5, obMax:109, size:"38DD/E"},
  {ubMin:83.5, ubMax:86, obMin:109, obMax:111.5, size:"38F"},
  {ubMin:83.5, ubMax:86, obMin:111.5, obMax:114, size:"38G"},
  {ubMin:88.5, ubMax:91, obMin:101.5, obMax:104, size:"40A"},
  {ubMin:88.5, ubMax:91, obMin:104, obMax:106.5, size:"40B"},
  {ubMin:88.5, ubMax:91, obMin:106.5, obMax:109, size:"40C"},
  {ubMin:88.5, ubMax:91, obMin:109, obMax:111.5, size:"40D"},
  {ubMin:88.5, ubMax:91, obMin:111.5, obMax:114, size:"40DD/E"},
  {ubMin:88.5, ubMax:91, obMin:114, obMax:116.5, size:"40F"},
  {ubMin:88.5, ubMax:91, obMin:116.5, obMax:119, size:"40G"},
  {ubMin:93.5, ubMax:96, obMin:106.5, obMax:109, size:"42A"},
  {ubMin:93.5, ubMax:96, obMin:109, obMax:111.5, size:"42B"},
  {ubMin:93.5, ubMax:96, obMin:111.5, obMax:114, size:"42C"},
  {ubMin:93.5, ubMax:96, obMin:114, obMax:116.5, size:"42D"},
  {ubMin:93.5, ubMax:96, obMin:116.5, obMax:119, size:"42DD/E"},
  {ubMin:93.5, ubMax:96, obMin:119, obMax:121.5, size:"42F"},
  {ubMin:93.5, ubMax:96, obMin:121.5, obMax:124, size:"42G"},
  {ubMin:98.5, ubMax:101, obMin:111.5, obMax:114, size:"44A"},
  {ubMin:98.5, ubMax:101, obMin:114, obMax:116.5, size:"44B"},
  {ubMin:98.5, ubMax:101, obMin:116.5, obMax:119, size:"44C"},
  {ubMin:98.5, ubMax:101, obMin:119, obMax:121.5, size:"44D"},
  {ubMin:98.5, ubMax:101, obMin:121.5, obMax:124, size:"44DD/E"},
  {ubMin:98.5, ubMax:101, obMin:124, obMax:126.5, size:"44F"},
  {ubMin:98.5, ubMax:101, obMin:126.5, obMax:129, size:"44G"},
];

let braUnit = "in";

const bandLabel = document.getElementById('bandLabel');
const bustLabel = document.getElementById('bustLabel');
const bandInput = document.getElementById('underbust');
const bustInput = document.getElementById('overbust');

// Remove old toggle code and add radio button logic for unit switching
const inRadio = document.getElementById('in-units');
const cmRadio = document.getElementById('cm-units');

function switchUnit(toUnit) {
  if (toUnit === braUnit) return;
  if (toUnit === 'in') {
    inRadio.checked = true;
    cmRadio.checked = false;
    bandLabel.innerHTML = 'Under Bust (in inches)';
    bustLabel.innerHTML = 'Over Bust (in inches)';
    bandInput.placeholder = "e.g. 32";
    bustInput.placeholder = "e.g. 36";
  } else {
    inRadio.checked = false;
    cmRadio.checked = true;
    bandLabel.innerHTML = 'Under Bust (in centimeter)';
    bustLabel.innerHTML = 'Over Bust (in centimeter)';
    bandInput.placeholder = "e.g. 80";
    bustInput.placeholder = "e.g. 92";
  }
  braUnit = toUnit;
  bandInput.value = '';
  bustInput.value = '';
  document.getElementById('bra-size-result').innerHTML = '';
  document.getElementById('bra-result-section').style.display = 'none';
  updateBraUnits();
}

if (inRadio && cmRadio) {
  inRadio.addEventListener('change', function() {
    if (inRadio.checked) switchUnit('in');
  });
  cmRadio.addEventListener('change', function() {
    if (cmRadio.checked) switchUnit('cm');
  });
}

function updateBraUnits() {
  const underbustUnit = document.getElementById("underbust-unit");
  const overbustUnit = document.getElementById("overbust-unit");
  underbustUnit.textContent = braUnit;
  overbustUnit.textContent = braUnit;
}

function resetBraCalculator() {
  document.getElementById("underbust").value = "";
  document.getElementById("overbust").value = "";
  document.getElementById("bra-result-section").style.display = "none";
  document.getElementById("bra-size-result").innerHTML = "";
}

function getCurrentUnit() {
  if (inRadio && inRadio.checked) return 'in';
  if (cmRadio && cmRadio.checked) return 'cm';
  return braUnit; // fallback
}

function calculateBraSize() {
  // Always use the currently selected radio button for unit
  braUnit = getCurrentUnit();
  updateBraUnits();

  let under = parseFloat(bandInput.value);
  let over = parseFloat(bustInput.value);
  const resultDiv = document.getElementById('bra-size-result');
  const resultSection = document.getElementById('bra-result-section');

  if (isNaN(under) || isNaN(over) || under <= 0 || over <= 0) {
    resultDiv.textContent = "Please enter valid measurements.";
    resultSection.style.display = 'block';
    return;
  }

  let found = null;
  if (braUnit === 'in') {
    for (let row of sizeChartInches) {
      if (
        under >= row.ubMin && under < row.ubMax + 1 &&
        over >= row.obMin && over < row.obMax + 1
      ) {
        found = row.size;
        break;
      }
    }
  } else {
    for (let row of sizeChartCm) {
      if (
        under >= row.ubMin && under < row.ubMax + 0.01 &&
        over >= row.obMin && over < row.obMax + 0.01
      ) {
        found = row.size;
        break;
      }
    }
  }

  if (found) {
    resultDiv.innerHTML = `<span>Your Bra Size is:</span><br><strong>${found}</strong>`;
  } else {
    resultDiv.innerHTML = `<span>Sorry, we could not find a bra size for your measurements.</span>`;
  }
  resultSection.style.display = 'block';
  resultSection.scrollIntoView({ behavior: "smooth", block: "center" });
}

// Header scroll effect
function handleScroll() {
  const header = document.querySelector("header");
  if (header) {
    if (window.scrollY === 0) {
      header.classList.remove("scrolled");
    } else {
      header.classList.add("scrolled");
    }
  }
}
window.addEventListener("scroll", handleScroll);
handleScroll();

// Set initial state
updateBraUnits();
