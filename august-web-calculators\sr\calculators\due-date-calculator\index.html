<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Besplatni kalkulator trudnoće - Kalkulator termina porođaja i gestacijskih godina 2025</title>
    <meta name="description" content="Izračunajte termin porođaja i gestacijske godine pomoću našeg besplatnog, tačnog kalkulatora trudnoće. Pratite svoju trudnoću nedelju po nedelju na osnovu poslednje menstruacije." />
    <meta name="keywords" content="kalkulator trudnoće, kalkulator termina porođaja, kalkulator gestacijskih godina, pra<PERSON><PERSON><PERSON> trud<PERSON>, ne<PERSON><PERSON> trud<PERSON>, LMP kalkulator, termin porođaja, buduće majke" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Besplatni kalkulator trudnoće - Kalkulator termina porođaja i gestacijskih godina" />
    <meta property="og:description" content="Izračunajte termin porođaja i gestacijske godine pomoću našeg besplatnog, tačnog kalkulatora trudnoće. Pratite svoju trudnoću nedelju po nedelju." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/sr/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/sr/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/sr/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/sr/calculators/">
            <img
              width="200"
              src="/sr/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Logo Kalkulatora"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Razgovarajte sa Augustom</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Калкулатор датума порођаја</h1>
        <p>Процените датум порођаја ваше бебе на основу последње менструације или датума зачећа. Користите наш калкулатор датума порођаја за планирање трудноће и припрему за долазак ваше бебе.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Poverenje buduće majke i zdravstveni radnici širom sveta</span>
          </div>

          <h2>Zašto koristiti naš kalkulator trudnoće?</h2>
          <p>Naš besplatni kalkulator trudnoće pomaže vam da odredite termin porođaja i trenutne gestacijske godine na osnovu poslednje menstruacije (LMP). Bilo da ste tek trudni ili pratite svoju trudnoću, ovaj alat pruža tačne kalkulacije koje će vam pomoći da se pripremite za dolazak bebe.</p>

          <h3>Ključne karakteristike:</h3>
          <ul>
            <li>Trenutno računanje termina porođaja na osnovu LMP</li>
            <li>Trenutne gestacijske godine u nedeljama i danima</li>
            <li>Lako upotrebljiv interfejs sa padajućim menijima</li>
            <li>Medicinski tačni proračuni prema WHO smernicama</li>
            <li>Besplatno za korišćenje bez registracije</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Izračunajte termin porođaja</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Današnji datum:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Trenutni datum" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Prvi dan poslednje menstruacije (LMP):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="LMP datum" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Postaviti na danas
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Obriši rezultate
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Vaš procenjeni termin porođaja:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Trenutne gestacijske godine:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Često postavljana pitanja o kalkulatorima trudnoće</h2>

        <div class="faq-item">
          <div class="faq-question">Koliko je tačan ovaj kalkulator trudnoće?</div>
          <div class="faq-answer">Naš kalkulator trudnoće koristi standardnu medicinsku formulu dodavanja 280 dana na poslednju menstruaciju. Ovaj metod je približno 95% tačan za predviđanje termina porođaja, mada individualne trudnoće mogu da variraju do dve nedelje.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Šta ako ne sećam tačan LMP datum?</div>
          <div class="faq-answer">Ako ne možete da se setite tačnog LMP datuma, pokušajte da procenite što je tačnije moguće. Vaš zdravstveni radnik može da koristi ultrazvučne merenja da pruži tačniji termin porođaja tokom prve prenatalne posete.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Mogu li da koristim ovaj kalkulator ako imam nepravilne menstruacije?
          </div>
          <div class="faq-answer">Ako imate nepravilne menstrualne cikluse, ovaj kalkulator može biti manje tačan. U takvim slučajevima, vaš doktor će verovatno koristiti ultrazvučno datiranje da preciznije odredi termin porođaja.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Kada treba da zakažem prvi prenatalni pregled?
          </div>
          <div class="faq-answer">
            Većina zdravstvenih radnika preporučuje zakazivanje prvog prenatalnog pregleda između 6-8 nedelja trudnoće. Koristite naš kalkulator da odredite trenutne gestacijske godine i planirajte u skladu s tim.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Koja je razlika između gestacijskih i fetalnih godina?
          </div>
          <div class="faq-answer">Gestacijske godine se računaju od poslednje menstruacije, dok se fetalne godine računaju od začeća (obično 2 nedelje kasnije). Medicinski radnici obično koriste gestacijske godine radi konzistentnosti.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
