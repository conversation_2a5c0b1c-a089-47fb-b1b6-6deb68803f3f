<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Δωρεάν Υπολογιστής Εγκυμοσύνης - Υπολογιστής Ημερομηνίας Τοκετού & Εμβρυϊκής Ηλικίας 2025</title>
    <meta name="description" content="Υπολογίστε την ημερομηνία τοκετού και την εμβρυϊκή ηλικία με τον δωρεάν, ακριβή υπολογιστή εγκυμοσύνης. Παρακολουθήστε το ταξίδι της εγκυμοσύνης εβδομάδα προς εβδομάδα βασισμένα στην τελευταία εμμηνόρροια." />
    <meta name="keywords" content="υπολογιστής εγκυμοσύνης, υπολογιστής ημερομηνίας τοκετού, υπολογιστής εμβρυϊκής ηλικίας, παρακολούθηση εγκυμοσύνης, εβδομάδες εγκυμοσύνης, υπολογιστής τελευταίας εμμηνόρροιας, ημερομηνία τοκετού, μελλοντικές μητέρες" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="Δωρεάν Υπολογιστής Εγκυμοσύνης - Υπολογιστής Ημερομηνίας Τοκετού & Εμβρυϊκής Ηλικίας" />
    <meta property="og:description" content="Υπολογίστε την ημερομηνία τοκετού και την εμβρυϊκή ηλικία με τον δωρεάν, ακριβή υπολογιστή εγκυμοσύνης. Παρακολουθήστε το ταξίδι της εγκυμοσύνης εβδομάδα προς εβδομάδα." />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/el/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/el/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/el/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/el/calculators/">
            <img
              width="200"
              src="/el/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="Λογότυπο Υπολογιστή"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Μιλήστε με τον August</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>Υπολογιστής Ημερομηνίας Τοκετού</h1>
        <p>Εκτιμήστε την ημερομηνία τοκετού του μωρού σας με βάση την τελευταία σας περίοδο ή την ημερομηνία σύλληψης. Χρησιμοποιήστε τον υπολογιστή ημερομηνίας τοκετού μας για να προγραμματίσετε την εγκυμοσύνη σας και να προετοιμαστείτε για την άφιξη του μωρού σας.</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">Εμπιστεύονται μελλοντικές μητέρες και επαγγελματίες υγείας παγκοσμίως</span>
          </div>

          <h2>Γιατί να Χρησιμοποιήσετε τον Υπολογιστή Εγκυμοσύνης μας;</h2>
          <p>Ο δωρεάν υπολογιστής εγκυμοσύνης μας σας βοηθά να προσδιορίσετε την ημερομηνία τοκετού του μωρού σας και την τρέχουσα εμβρυϊκή ηλικία βασισμένα στην τελευταία εμμηνόρροια (ΤΕ). Είτε είστε νέα έγκυος είτε παρακολουθείτε το ταξίδι της εγκυμοσύνης, αυτό το εργαλείο παρέχει ακριβείς υπολογισμούς για να σας βοηθήσει να σχεδιάσετε για την άφιξη του μωρού σας.</p>

          <h3>Βασικά Χαρακτηριστικά:</h3>
          <ul>
            <li>Άμεσος υπολογισμός ημερομηνίας τοκετού βασισμένος στην ΤΕ</li>
            <li>Τρέχουσα εμβρυϊκή ηλικία σε εβδομάδες και ημέρες</li>
            <li>Εύκολη στη χρήση διεπαφή με αναπτυσσόμενα μενού</li>
            <li>Ιατρικώς ακριβείς υπολογισμοί σύμφωνα με τις οδηγίες του ΠΟΥ</li>
            <li>Δωρεάν χρήση χωρίς απαίτηση εγγραφής</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">Υπολογίστε την Ημερομηνία Τοκετού σας</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">Σημερινή Ημερομηνία:</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="Τρέχουσα ημερομηνία" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">Πρώτη Ημέρα της Τελευταίας Εμμηνόρροιας (ΤΕ):</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="Ημερομηνία ΤΕ" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                Ορισμός σε Σήμερα
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                Καθαρισμός Αποτελεσμάτων
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">Η Εκτιμώμενη Ημερομηνία Τοκετού σας:</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  Τρέχουσα Εμβρυϊκή Ηλικία:
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>Συχνές Ερωτήσεις για Υπολογιστές Εγκυμοσύνης</h2>

        <div class="faq-item">
          <div class="faq-question">Πόσο ακριβής είναι αυτός ο υπολογιστής εγκυμοσύνης;</div>
          <div class="faq-answer">Ο υπολογιστής εγκυμοσύνης μας χρησιμοποιεί τον τυπικό ιατρικό τύπο προσθέτοντας 280 ημέρες στην τελευταία εμμηνόρροια. Αυτή η μέθοδος είναι περίπου 95% ακριβής για την πρόβλεψη της ημερομηνίας τοκετού, αν και οι ατομικές εγκυμοσύνες μπορεί να ποικίλλουν έως δύο εβδομάδες.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">Τι γίνεται αν δεν θυμάμαι την ακριβή ημερομηνία ΤΕ;</div>
          <div class="faq-answer">Αν δεν μπορείτε να θυμηθείτε την ακριβή ημερομηνία ΤΕ, προσπαθήστε να εκτιμήσετε όσο πιο κοντά γίνεται. Ο γιατρός σας μπορεί να χρησιμοποιήσει μετρήσεις υπερήχων για να παρέχει μια πιο ακριβή ημερομηνία τοκετού κατά την πρώτη προγεννητική επίσκεψη.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Μπορώ να χρησιμοποιήσω αυτόν τον υπολογιστή αν έχω ανώμαλους κύκλους;
          </div>
          <div class="faq-answer">Αν έχετε ανώμαλους εμμηνορροϊκούς κύκλους, αυτός ο υπολογιστής μπορεί να είναι λιγότερο ακριβής. Σε τέτοιες περιπτώσεις, ο γιατρός σας θα χρησιμοποιήσει πιθανότατα χρονολόγηση με υπέρηχο για να προσδιορίσει την ημερομηνία τοκετού πιο ακριβώς.</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Πότε πρέπει να κανονίσω το πρώτο προγεννητικό ραντεβού;
          </div>
          <div class="faq-answer">
            Οι περισσότεροι γιατροί συνιστούν να κανονίσετε το πρώτο προγεννητικό ραντεβού μεταξύ 6-8 εβδομάδων εγκυμοσύνης. Χρησιμοποιήστε τον υπολογιστή μας για να προσδιορίσετε την τρέχουσα εμβρυϊκή ηλικία και να σχεδιάσετε αναλόγως.
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            Ποια είναι η διαφορά μεταξύ εμβρυϊκής ηλικίας και ηλικίας εμβρύου;
          </div>
          <div class="faq-answer">Η εμβρυϊκή ηλικία υπολογίζεται από την τελευταία εμμηνόρροια, ενώ η ηλικία εμβρύου υπολογίζεται από τη σύλληψη (συνήθως 2 εβδομάδες αργότερα). Οι ιατροί συνήθως χρησιμοποιούν την εμβρυϊκή ηλικία για συνέπεια.</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
