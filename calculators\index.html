<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Health Calculators | MeetAugust</title>
  <meta name="description" content="Welcome to MeetAugust's Health Calculators. Start with our advanced Calorie Calculator to discover your daily needs.">
  <link rel="stylesheet" href="/calculators/landing.css">
  <link rel="preload" href="/public/august_logo_green.svg" as="image">
  <style>
    :root {
      --primary-color: #206e55;
      --primary-dark: #1a5a47;
      --primary-light: #f0f9f4;
      --text-primary: #111827;
      --text-secondary: #374151;
      --background-light: #f9fafb;
      --background-white: #fff;
      --border-radius: 12px;
      --shadow-lg: 0 8px 24px rgba(0,0,0,0.08);
    }
    body {
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      background: var(--background-light);
      margin: 0;
      color: var(--text-primary);
      min-height: 100vh;
    }
    header {
      background-color: transparent;
      padding: 8px 0px;
      border-bottom: none;
      position: sticky;
      top: 0;
      z-index: 1000;
      transition: all 0.3s ease;
      box-shadow: none;
    }
    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      margin: 0 auto;
    }
    .logo {
      display: flex;
      align-items: center;
    }
    .logo img {
      height: 60px;
      width: auto;
    }
    .nav {
      display: flex;
      align-items: center;
      gap: 32px;
    }
    .talk-to-august {
      background-color: #206e55;
      color: white !important;
      padding: 10px 18px;
      border-radius: 6px;
      text-decoration: none;
      font-weight: 500;
      font-size: 16px;
      transition: background-color 0.2s ease;
    }
    .talk-to-august:hover {
      background-color: #1a5a47;
      color: white !important;
    }
    .hero {
      text-align: center;
      margin-top: 40px;
      margin-bottom: 32px;
    }
    .hero h1 {
      color: var(--primary-color);
      font-size: 2.8rem;
      font-weight: 700;
      margin-bottom: 12px;
    }
    .hero p {
      color: var(--text-secondary);
      font-size: 1.25rem;
      margin-bottom: 0;
    }
    .main-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 32px;
    }
    .card {
      background: var(--background-white);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow-lg);
      padding: 32px 28px 28px 28px;
      max-width: 400px;
      width: 100%;
      text-align: center;
      margin-bottom: 32px;
      border: 1px solid #e5e7eb;
      transition: box-shadow 0.2s;
    }
    .card:hover {
      box-shadow: 0 12px 32px rgba(32,110,85,0.12);
    }
    .card-title {
      font-size: 1.5rem;
      color: var(--primary-color);
      font-weight: 600;
      margin-bottom: 10px;
    }
    .card-desc {
      color: var(--text-secondary);
      font-size: 1.1rem;
      margin-bottom: 18px;
    }
    .card-action {
      display: flex;
      justify-content: center;
    }
    .card-action a {
      background: var(--primary-color);
      color: #fff;
      text-decoration: none;
      padding: 12px 32px;
      border-radius: 8px;
      font-size: 1.1rem;
      font-weight: 500;
      transition: background 0.2s;
      box-shadow: 0 2px 8px rgba(32,110,85,0.08);
    }
    .card-action a:hover {
      background: var(--primary-dark);
    }
    @media (max-width: 600px) {
      .navbar { flex-direction: column; padding: 16px 8px; }
      .hero h1 { font-size: 2rem; }
      .main-content { margin-top: 16px; }
      .card { padding: 20px 8px; }
    }
    
header {
  background-color: transparent;
  padding: 8px 0px;
  border-bottom: none;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: none;
}

header.scrolled {
  background-color: #f8f9fa;
  padding: 4px 0px;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  /* max-width: 1430px; */
  margin: 0 auto;
}
.logo {
  display: flex;
  align-items: center;
}
.logo img {
  height: 60px;
  width: auto;
}
.nav {
  display: flex;
  align-items: center;
  gap: 32px;
}
.nav-links {
  display: flex;
  align-items: center;
  gap: 24px;
  list-style: none;
  margin: 0;
  padding: 0;
}
.nav-links a {
  color: #111827;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: color 0.2s ease;
}
.nav-links a:hover {
  color: #206e55;
}
.nav-links a.active {
  color: #206e55;
}
.talk-to-august {
  background-color: #206e55;
  color: white !important;
  padding: 10px 18px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: background-color 0.2s ease;
}
.talk-to-august:hover {
  background-color: #1a5a47;
  color: white !important;
}
    /* Language Switcher MUI-like Styles */
    .language-switcher {
      display: flex;
      align-items: center;
      margin-right: 16px;
    }
    #language-select {
      min-width: 120px;
      border: 1px solid #e5e7eb;
      border-radius: 5px;
      height: 45px;
      padding: 8px 16px;
      font-size: 1rem;
      color: #374151;
      background: #fff;
      outline: none;
      transition: border-color 0.2s;
      box-shadow: none;
      appearance: none;
      cursor: pointer;
    }
    #language-select:focus {
      border-color: #4caf50;
      box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
    }
    #language-select option {
      font-size: 1rem;
      padding: 10px 16px;
    }
    @media (max-width: 600px) {
      #language-select {
        min-width: 80px;
        font-size: 0.875rem;
        padding: 6px 8px;
      }
      #language-select option {
        font-size: 0.875rem;
      }
    }
  </style>
</head>
<body>
    <header>
        <div class="navbar">
          <div class="logo">
            <img width="200" src="/{{lang}}/calculators/assets/august_logo_green_nd4fn9.svg" alt="{{logo_alt}}" />
          </div>
          <div class="nav">
            <div class="language-switcher" id="language-switcher">
              <select id="language-select">
                <!-- Options will be populated by JS -->
              </select>
            </div>
            <a href="https://app.meetaugust.ai/redirect/wa?message=Hello%20August" class="talk-to-august">{{talk_to_august}}</a>
          </div>
        </div>
      </header>
  <section class="hero">
    <h1>Welcome to Health Calculators</h1>
    <p>Empowering your health journey with science-backed tools.<br>Start with our advanced Calorie Calculator to discover your daily needs.</p>
  </section>
  <main class="main-content">
    <div class="card">
      <div class="card-title">Calorie Calculator</div>
      <div class="card-desc">Find out your daily calorie requirements for weight loss, maintenance, or muscle gain. Fast, accurate, and free.</div>
      <div class="card-action">
        <a href="/calculators/calorie/">Go to Calorie Calculator</a>
      </div>
    </div>
    <div class="card">
      <div class="card-title">BMI Calculator</div>
      <div class="card-desc">Instantly check your Body Mass Index (BMI) and see which weight category you fall into. Designed for adults 20+ years. Fast, accurate, and free.</div>
      <div class="card-action">
        <a href="/calculators/bmi/">Go to BMI Calculator</a>
      </div>
    </div>
    <!-- You can add more cards for other calculators here in the future -->
  </main>
  <script>
  const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
  const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
  const select = document.getElementById('language-select');
  const currentLang = window.location.pathname.split('/')[1] || 'en';
  locales.forEach(l => {
    const opt = document.createElement('option');
    opt.value = l;
    opt.textContent = languageNames[l] || l;
    if (l === currentLang) opt.selected = true;
    select.appendChild(opt);
  });
  select.addEventListener('change', function() {
    const newLang = this.value;
    let path = window.location.pathname.split('/');
    if (locales.includes(path[1])) { path[1] = newLang; }
    else { path = ['', newLang, ...path.slice(1)]; }
    localStorage.setItem('preferredLang', newLang);
    window.location.pathname = path.join('/');
  });
  // Persist language choice
  if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
    select.value = localStorage.getItem('preferredLang');
    select.dispatchEvent(new Event('change'));
  }

  // Header scroll effect
function handleScroll() {
  const header = document.querySelector("header");
  if (header) {
    if (window.scrollY === 0) {
      header.classList.remove("scrolled");
    } else {
      header.classList.add("scrolled");
    }
  }
}

// Add scroll event listener
window.addEventListener("scroll", handleScroll);

// Initialize header state
handleScroll();
  </script>
</body>
</html>
