<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>無料妊娠計算機 - 出産予定日・妊娠週数計算機 2025</title>
    <meta name="description" content="無料で正確な妊娠計算機で出産予定日と妊娠週数を計算しましょう。最終月経日に基づいて妊娠の経過を週ごとに追跡できます。" />
    <meta name="keywords" content="妊娠計算機, 出産予定日計算機, 妊娠週数計算機, 妊娠トラッカー, 妊娠週数, 最終月経日計算機, 出産予定日, 妊娠中の女性" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="無料妊娠計算機 - 出産予定日・妊娠週数計算機" />
    <meta property="og:description" content="無料で正確な妊娠計算機で出産予定日と妊娠週数を計算しましょう。妊娠の経過を週ごとに追跡できます。" />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/ja/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/ja/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/ja/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/ja/calculators/">
            <img
              width="200"
              src="/ja/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="計算機ロゴ"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >Augustと話す</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>無料妊娠計算機・出産予定日計算機</h1>
        <p>正確で使いやすい妊娠計算機で出産予定日を計算し、妊娠週数を追跡しましょう</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">世界中の妊娠中の女性と医療従事者に信頼されています</span>
          </div>

          <h2>なぜ私たちの妊娠計算機を使うのか？</h2>
          <p>無料の妊娠計算機は、最終月経日（LMP）に基づいて赤ちゃんの出産予定日と現在の妊娠週数を決定するのに役立ちます。妊娠したばかりでも、妊娠の経過を追跡していても、このツールは赤ちゃんの誕生に向けた計画を立てるのに役立つ正確な計算を提供します。</p>

          <h3>主な機能：</h3>
          <ul>
            <li>LMPに基づく即座の出産予定日計算</li>
            <li>現在の妊娠週数（週と日）</li>
            <li>ドロップダウンメニューを使った使いやすいインターフェース</li>
            <li>WHO ガイドラインに従った医学的に正確な計算</li>
            <li>登録不要で無料使用</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">出産予定日を計算する</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">今日の日付：</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="現在の日付" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">最終月経開始日（LMP）：</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="LMPの日付" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                今日に設定
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                結果をクリア
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">予定出産日：</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  現在の妊娠週数：
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>妊娠計算機についてよくある質問</h2>

        <div class="faq-item">
          <div class="faq-question">この妊娠計算機の精度はどの程度ですか？</div>
          <div class="faq-answer">妊娠計算機は、最終月経日に280日を加える標準的な医学的公式を使用しています。この方法は出産予定日を予測するのに約95%の精度がありますが、個人の妊娠は最大2週間程度変動する可能性があります。</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">正確なLMP日付を覚えていない場合はどうすればよいですか？</div>
          <div class="faq-answer">正確なLMP日付を覚えていない場合は、できるだけ正確に推定してください。医療提供者は、最初の妊婦健診で超音波測定を使用してより正確な出産予定日を提供できます。</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            生理不順の場合でもこの計算機を使用できますか？
          </div>
          <div class="faq-answer">月経周期が不規則な場合、この計算機の精度は低くなる可能性があります。そのような場合、医師は超音波による日付測定を使用してより正確に出産予定日を決定する可能性があります。</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            最初の妊婦健診はいつ予約すべきですか？
          </div>
          <div class="faq-answer">
            ほとんどの医療提供者は、妊娠6-8週間の間に最初の妊婦健診を予約することを推奨しています。計算機を使用して現在の妊娠週数を確認し、それに応じて計画を立ててください。
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            妊娠週数と胎児週数の違いは何ですか？
          </div>
          <div class="faq-answer">妊娠週数は最終月経日から計算され、胎児週数は受精から計算されます（通常2週間後）。医療従事者は一貫性のために通常妊娠週数を使用します。</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
