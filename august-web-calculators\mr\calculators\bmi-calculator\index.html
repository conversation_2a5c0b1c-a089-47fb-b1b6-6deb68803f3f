<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>प्रौढांसाठी BMI कॅल्क्युलेटर - अचूक BMI साधन | MeetAugust</title>
    <meta
      name="description"
      content="आमच्या प्रगत साधनाने तुमचा BMI मोजा. त्वरित निकाल मिळवा आणि तुमची वजन श्रेणी पहा. मोफत, अचूक आणि वैज्ञानिकदृष्ट्या आधारित."
    />
    <meta
      name="keywords"
      content="bmi कॅल्क्युलेटर, बॉडी मास इंडेक्स, वजन श्रेणी, आरोग्य कॅल्क्युलेटर"
    />
    <meta name="author" content="MeetAugust" />
    <meta
      property="og:title"
      content="प्रौढांसाठी BMI कॅल्क्युलेटर - अचूक BMI साधन"
    />
    <meta
      property="og:description"
      content="तुमचा BMI मोजा आणि तुमची वजन श्रेणी त्वरित पहा."
    />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="प्रौढांसाठी BMI कॅल्क्युलेटर - अचूक BMI साधन"
    />
    <meta
      name="twitter:description"
      content="तुमचा BMI मोजा आणि तुमची वजन श्रेणी त्वरित पहा."
    />
    <link rel="canonical" href="https://www.meetaugust.ai/mr/calculators/bmi-calculator" />
    <link rel="icon" href="/mr/calculators/assets/favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="/mr/calculators/bmi-calculator/style.css">
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
        <div class="navbar">
          <div class="logo">
            <a href="/mr/calculators/">
                <img
              width="200"
              src="/mr/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="कॅल्क्युलेटर लोगो"
            />
            </a>
          </div>
          <div class="nav">
            <div class="language-switcher" id="language-switcher">
              <select id="language-select">
                <!-- Options will be populated by JS -->
              </select>
            </div>
            <a
              href="https://app.meetaugust.ai/join/app?utm=bmi_calculator_topnav"
              class="talk-to-august"
              >ऑगस्टशी बोला</a
            >
          </div>
        </div>
      </header>
        <div id="container">
      <main>
        <div class="page-header">
          <h1 class="page-title">प्रौढांसाठी BMI कॅल्क्युलेटर</h1>
          <div class="page-info">
            <div class="info-badge">
              <div class="shield-icon">✓</div>
              <span>सर्वांसाठी</span>
            </div>
          </div>
        </div>

        <div class="content-grid">
          <div class="info-section">
            <h2>प्रौढांसाठी BMI समजून घ्या: तुम्हाला काय माहित असावे</h2>
            <p class="info-text">
              प्रौढांसाठी BMI कॅल्क्युलेटर हे तुमचे वजन तुमच्या उंचीसाठी योग्य आहे का हे तपासण्यासाठी एक विश्वासार्ह साधन आहे. ...
            </p>

            <img
              src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="BMI Scale"
              class="weight-image"
            />

            <div style="margin-top: 30px">
              <h3
                style="
                  color: var(--primary-color);
                  font-size: 20px;
                  margin-bottom: 15px;
                "
              >
                प्रौढांसाठी मोफत BMI कॅल्क्युलेटर (वय २०+)
              </h3>
              <p style="color: var(--text-secondary); margin-bottom: 15px">
                हा प्रौढ BMI कॅल्क्युलेटर वापरून त्वरित तुमचा BMI ठरवा आणि तुम्ही कोणत्या वजन श्रेणीत येता ते पहा — कमी वजन, सामान्य वजन, जास्त वजन किंवा स्थूलता. हा कॅल्क्युलेटर विशेषतः २० वर्षांपेक्षा जास्त वयाच्या प्रौढांसाठी डिझाइन केला आहे.
              </p>
              <!-- <p style="color: var(--text-secondary)">
                BMI हे एकूण आरोग्य आणि वजनाशी संबंधित संभाव्य जोखमीचे मूल्यांकन करण्यासाठी वापरल्या जाणार्‍या अनेक साधनांपैकी एक आहे. हे वैद्यकीय इतिहास, जीवनशैली सवयी, शारीरिक तपासणीचे निकाल आणि प्रयोगशाळेच्या चाचण्यांसारख्या इतर क्लिनिकल मूल्यांकनांसह समजले पाहिजे. -->
                <!-- <a href="#" style="color: var(--primary-color)">
                  बॉडी मास इंडेक्सबद्दल अधिक जाणून घ्या
                </a>
                आणि ते तुमच्या आरोग्य प्रोफाइलमध्ये कसे बसते ते पहा. नेहमीच वैयक्तिक सल्ल्यासाठी आरोग्यसेवा प्रदात्याचा सल्ला घ्या — हे साधन केवळ शैक्षणिक उद्दिष्टांसाठी आहे आणि वैद्यकीय सल्ल्याचा पर्याय नाही.
              </p> -->
            </div>
          </div>

          <div class="calculator-section">
            <div style="display: flex; justify-content: space-between;" >

              <h3 class="calculator-title">BMI कॅल्क्युलेटर</h3>
              <button class="download-btn  neon-pulse" style="margin-left: 2rem; margin-top: 0; display: none; align-self: flex-start; font-size: 1.15rem; padding: 1rem 2rem;" id="download-pdf-btn" onclick="downloadBMIResultsPDF()"><span style="font-size:1.5em;vertical-align:middle;">📄</span> Download BMI Results</button>
            </div>

            <div class="unit-selector">
              <div class="unit-option">
                <input
                  type="radio"
                  id="us-units"
                  name="units"
                  value="us"
                  checked
                />
                <label for="us-units">{{bmi_us_units}}</label>
              </div>
              <div class="unit-option">
                <input
                  type="radio"
                  id="metric-units"
                  name="units"
                  value="metric"
                />
                <label for="metric-units">{{bmi_metric_units}}</label>
              </div>
              <span class="reset-link" onclick="resetCalculator()">{{bmi_reset}}</span>
            </div>

            <div class="form-group">
              <label class="form-label">{{bmi_height}}</label>
              <div class="input-group" id="height-inputs">
                <input
                  type="number"
                  class="form-input"
                  id="height-feet"
                  placeholder="0"
                  min="0"
                  max="10"
                />
                <span class="unit-label">{{bmi_feet}}</span>
                <input
                  type="number"
                  class="form-input"
                  id="height-inches"
                  placeholder="0"
                  min="0"
                  max="11"
                />
                <span class="unit-label">{{bmi_inches}}</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">{{bmi_weight}}</label>
              <div class="input-group">
                <input
                  type="number"
                  class="form-input"
                  id="weight"
                  placeholder="0"
                  min="0"
                />
                <span class="unit-label" id="weight-unit">{{bmi_pounds}}</span>
              </div>
            </div>

            <button class="calculate-btn" onclick="calculateBMI()">
              {{bmi_calculate}}
            </button>
          </div>
        </div>

        <div class="note-section">
          <p>
            <strong>{{bmi_note}}</strong> {{bmi_note_text}}
            <em>{{bmi_formula}}</em>
            {{bmi_formula_example}}
          </p>
        </div>
      </main>

<!-- Result section moved below main content -->
<div class="result-section-wrapper">
  <div class="result-section" id="result-section">
    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
      <div>
        <div class="bmi-value" id="bmi-value">0.0</div>
        <div class="bmi-category" id="bmi-category"></div>
        <div class="bmi-description" id="bmi-description"></div>
      </div>
    </div>
  </div>
</div>

    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="/mr/calculators/bmi-calculator/script.js"></script>
    <script>
      const locales = ["en","fr","de","es","it","pt","ru","ja","ko","he","bg","fi","hr","lv","mk","mr","sk","sl","sr","tl","el"];
      const languageNames = {en:"English",fr:"Français",de:"Deutsch",es:"Español",it:"Italiano",pt:"Português",ru:"Русский",ja:"日本語",ko:"한국어",he:"עברית",bg:"Български",fi:"Suomi",hr:"Hrvatski",lv:"Latviešu",mk:"Македонски",mr:"मराठी",sk:"Slovenčina",sl:"Slovenščina",sr:"Српски",tl:"Tagalog",el:"Ελληνικά"};
      const select = document.getElementById('language-select');
      const currentLang = window.location.pathname.split('/')[1] || 'en';
      locales.forEach(l => {
        const opt = document.createElement('option');
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener('change', function() {
        const newLang = this.value;
        let path = window.location.pathname.split('/');
        if (locales.includes(path[1])) { path[1] = newLang; }
        else { path = ['', newLang, ...path.slice(1)]; }
        localStorage.setItem('preferredLang', newLang);
        window.location.pathname = path.join('/');
      });
      // Persist language choice
      if (localStorage.getItem('preferredLang') && localStorage.getItem('preferredLang') !== currentLang) {
        select.value = localStorage.getItem('preferredLang');
      }
    </script>
  </body>
</html> 